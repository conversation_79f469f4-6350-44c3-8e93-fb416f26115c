version: "3.7"
services:
  siegerweb:
    build:
      context: .
      dockerfile: Dockerfile
    restart: unless-stopped
    networks: [dokploy-network]            # ⬅️ solo la red de Dokploy
    labels:
      - traefik.enable=true
      - traefik.docker.network=dokploy-network  # ⬅️ una sola vez, apuntando a esa red
      - traefik.http.routers.siegerweb.rule=Host(`test.sieger.com.ar`)
      - traefik.http.routers.siegerweb.entrypoints=websecure
      - traefik.http.routers.siegerweb.tls=true
      - traefik.http.routers.siegerweb.tls.certresolver=letsencrypt
      - traefik.http.services.siegerweb.loadbalancer.server.port=80
      # SPA: fallback 404 -> /index.html
      - traefik.http.middlewares.siegerweb-spa.errors.status=404
      - traefik.http.middlewares.siegerweb-spa.errors.service=siegerweb@docker
      - traefik.http.middlewares.siegerweb-spa.errors.query=/index.html
      # redirect raíz -> /index.html  (ojo: $ se escapa como $$ en compose)
      - traefik.http.middlewares.siegerweb-root.redirectregex.regex=^/$$
      - traefik.http.middlewares.siegerweb-root.redirectregex.replacement=/index.html
      - traefik.http.middlewares.siegerweb-root.redirectregex.permanent=true
      - traefik.http.routers.siegerweb.middlewares=siegerweb-spa,siegerweb-root

networks:
  dokploy-network:
    external: true  # ⬅️ reusar la red existente de Dokploy, no crear otra
