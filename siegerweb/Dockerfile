# ---------- build ----------
FROM node:20-alpine AS build
WORKDIR /usr/src/app

# si el código está en subcarpeta 'canelo', copiá desde ahí
COPY siegerweb/package*.json ./
RUN npm ci

COPY siegerweb/ ./
# si necesitas .env en build (Vite lee VITE_* en build)
COPY siegerweb/.env .env

RUN npm run build

# ---------- run ----------
FROM nginx:alpine
# opcional: tu nginx.conf de SPA
# COPY ./nginx.conf /etc/nginx/conf.d/default.conf
COPY --from=build /usr/src/app/dist /usr/share/nginx/html
RUN chmod -R 755 /usr/share/nginx/html
EXPOSE 80
#CMD ["nginx", "-g", "daemon off;"]