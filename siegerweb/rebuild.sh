#!/bin/bash

# Ruta de trabajo
cd /srv/webs/

# Hacemos pull si hay repo
if [ -d "/srv/webs/siegerweb/.git" ]; then
  echo "📥 Haciendo pull del repositorio en siegerweb..."
  cd /srv/webs/siegerweb
  git config --global --add safe.directory /srv/webs/siegerweb 2>/dev/null
  git pull origin $(git rev-parse --abbrev-ref HEAD)
  cd /srv/webs/
else
  echo "🚫 No se detectó repositorio Git en siegerweb. Skipping pull."
fi

echo "🛑 Deteniendo solo el servicio siegerweb..."
docker compose stop siegerweb

echo "⚙️ Reconstruyendo imagen de siegerweb..."
docker compose build siegerweb

echo "🚀 Levantando siegerweb con nueva imagen..."
docker compose up -d siegerweb

echo "✅ Servicio siegerweb actualizado y corriendo."
