/** @type {import('tailwindcss').Config} */
export default {
  content: ["./index.html", "./src/**/*.{js,ts,jsx,tsx}"],
  theme: {
    fontFamily: {
      corbert: ["Corbert", "sans-serif"],
      corbertMedium: ["CorbertMedium", "sans-serif"],
      corbertBold: ["CorbertBold", "sans-serif"],
      poppins: ["Poppins", "sans-serif"],
      poppinsLight: ["PoppinsLight", "sans-serif"],
      gotham: ["Gotham", "sans-serif"],
      gothamMedium: ["GothamMedium", "sans-serif"],
      gothamBold: ["GothamBold", "sans-serif"],
    },
    extend: {
      colors: {
        "neutrals-gray": "#6B7280",
        "secondary-gray": "#A8ACB1",
        "tertiary-gray": "#8D8D8D",
        "neutrals-blackie": "#111827",
        "neutrals-whiteSmoke": "#EFEFEF",
        "neutrals-slateGray": "#4B5563",
        "primary-red": "#E60012",
        "secondary-red": "#FB333E",
        "tertiary-red": "#EF3340",
        "neutrals-puppy": "#47B7D7",
        "neutrals-dogAdult": "#1946B9",
        "neutrals-dogSenior": "#662840",
        "neutrals-kitten": "#FBCDD5",
        "neutrals-catAdult": "#004ABA",
        "neutrals-catSenior": "#662840",
      },
      keyframes: {
        "arrow-down-up": {
          "0%": { transform: "translate(-50%, 0)" },
          "25%": { transform: "translate(-50%, 10px)" },
          "50%": { transform: "translate(-50%, 10px)" },
          "75%": { transform: "translate(-50%, 0)" },
          "100%": { transform: "translate(-50%, 0)" },
        },
        "slide-in-right": {
          "0%": { transform: "translateX(100%)" },
          "100%": { transform: "translateX(0)" },
        },
        "slide-out-left": {
          "0%": { transform: "translateX(0%)" },
          "100%": { transform: "translateX(100%)" },
        },
      },
      animation: {
        "arrow-down-up": "arrow-down-up 3s ease-in-out infinite",
        "slide-in-right": "slide-in-right 0.3s ease-out",
        "slide-out-left": "slide-out-left 0.3s ease-out",
      },
    },
  },
  plugins: [],
};
