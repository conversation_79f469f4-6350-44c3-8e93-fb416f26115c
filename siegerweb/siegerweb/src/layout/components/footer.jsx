import SuscribeSection from "../components/suscribe-section";
import logo1x from "@/assets/images/footer/logo-footer.webp";
import logo2x from "@/assets/images/footer/<EMAIL>";
import logo3x from "@/assets/images/footer/<EMAIL>";
import alicanLogo from "@/assets/icons/alican-logo.svg";
// import usePetStore from "@/stores/use-pet-store";
import { Link } from "react-router-dom";
import { Mail, MapPin, Phone } from "lucide-react";

const Footer = () => {
  return (
    <footer className="flex w-full flex-col items-center justify-start ">
      <SuscribeSection />
      <div className="px-8 md:px-14 pt-5 md:pt-12">
        <div className="grid grid-cols-1 md:grid-cols-5 gap-2 lg:gap-8">
          {/* Logo y descripción */}
          <div className="col-span-1">
            <div className="flex items-center lg:mb-4 gap-4">
              <picture className="max-w-[70px]">
                <source
                  media="(min-width: 300px)"
                  srcSet={`${logo1x} 1x, ${logo2x} 2x, ${logo3x} 3x`}
                />
                <img src={logo1x} alt="Sieger Logo" />
              </picture>
              <img src={alicanLogo} alt="Alican" className="max-w-9 -mt-1" />
            </div>
            {/* <div className="flex space-x-3 items-center">
              <a
                href="https://www.facebook.com/SiegerOficial"
                target="_blank"
                rel="noopener noreferrer"
              >
                <img src={facebook} alt="Facebook Logo" className="" />
              </a>
              <a
                href="https://www.instagram.com/siegeroficial/"
                target="_blank"
                rel="noopener noreferrer"
              >
                <img src={instagram} alt="Instagram Logo" className="" />
              </a>
              <a
                href="https://ar.linkedin.com/company/alican-sieger"
                target="_blank"
                rel="noopener noreferrer"
              >
                <img src={linkedin} alt="Linkedin Logo" className="" />
              </a>
              <a
                href="https://www.youtube.com/@SiegerAr"
                target="_blank"
                rel="noopener noreferrer"
              >
                <img src={youtube} alt="Youtube Logo" className="" />
              </a>
            </div> */}
          </div>

          {/* Perros */}
          <div className="hidden md:block col-span-1">
            <h3 className="font-semibold text-gray-800 mb-2 font-gothamMedium ">
              Perros
            </h3>
            <ul className="space-y-2 text-sm text-gray-600 font-corbert">
              <li>
                <a
                  href="#"
                  className="hover:text-secondary-red transition-colors duration-200"
                >
                  Puppy
                </a>
              </li>
              <li>
                <a
                  href="#"
                  className="hover:text-secondary-red transition-colors duration-200"
                >
                  Adult
                </a>
              </li>
              <li>
                <a
                  href="#"
                  className="hover:text-secondary-red transition-colors duration-200"
                >
                  Senior
                </a>
              </li>
              <li>
                <a
                  href="#"
                  className="hover:text-secondary-red transition-colors duration-200"
                >
                  Criadores
                </a>
              </li>
              <li>
                <a
                  href="#"
                  className="hover:text-secondary-red transition-colors duration-200"
                >
                  Especiales
                </a>
              </li>
            </ul>
          </div>

          {/* Gatos */}
          <div className="hidden md:block col-span-1">
            <h3 className="font-semibold text-gray-800 mb-2 font-gothamMedium ">
              Gatos
            </h3>
            <ul className="space-y-2 text-sm text-gray-600 font-corbert">
              <li>
                <a
                  href="#"
                  className="hover:text-secondary-red transition-colors duration-200"
                >
                  Kitten
                </a>
              </li>
              <li>
                <a
                  href="#"
                  className="hover:text-secondary-red transition-colors duration-200"
                >
                  Adult
                </a>
              </li>
              <li>
                <a
                  href="#"
                  className="hover:text-secondary-red transition-colors duration-200"
                >
                  Senior
                </a>
              </li>
              <li>
                <a
                  href="#"
                  className="hover:text-secondary-red transition-colors duration-200"
                >
                  Criadores
                </a>
              </li>
              <li>
                <a
                  href="#"
                  className="hover:text-secondary-red transition-colors duration-200"
                >
                  Especiales
                </a>
              </li>
            </ul>
          </div>

          {/* Links */}
          <div className="col-span-1">
            <div className="mb-2 md:mb-6">
              <h3 className="font-semibold text-gray-800 mb-2 font-gothamMedium ">
                Links
              </h3>
              <ul className="space-y-2 text-sm text-gray-600 font-corbert">
                <li>
                  <a
                    href="#"
                    className="hover:text-secondary-red transition-colors duration-200"
                  >
                    Nosotros
                  </a>
                </li>
                {/* <li>
                  <a href="#" className="hover:text-secondary-red transition-colors duration-200">
                    Donde comprar
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-secondary-red transition-colors duration-200">
                    Blog
                  </a>
                </li> */}
                <li>
                  <a
                    href="#"
                    className="hover:text-secondary-red transition-colors duration-200"
                  >
                    Sieger Vet
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    className="hover:text-secondary-red transition-colors duration-200"
                  >
                    Calculá tu porción
                  </a>
                </li>
              </ul>
            </div>
          </div>
          {/* Contacto */}
          <div className="col-span-1">
            <h3 className="font-semibold text-gray-800 mb-2 font-gothamMedium ">
              Contacto
            </h3>
            <div className="space-y-2 text-sm text-gray-600 font-corbert">
              <div className="flex gap-2 hover:text-secondary-red transition-colors duration-200 items-center">
                <Mail className="w-4" />
                <a href="mailto:<EMAIL>"><EMAIL></a>
              </div>
              <div className="flex gap-2 hover:text-secondary-red transition-colors duration-200 items-center">
                <Phone className="w-4" />
                <a href="tel:(0810) 222 - 0738">(0810) 222 - 0738</a>
              </div>
              <div className="flex gap-2  items-start">
                <MapPin className="w-4" />
                <div>
                  <div>Ruta Nac. 36 KM 647.5</div>
                  <div>Alcira Gigena - Córdoba</div>
                  <div>Argentina</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Footer bottom */}
        <div className="border-t border-gray-200 mt-3 p-2 font-corbert">
          <div className="flex flex-col md:flex-row justify-between items-center text-xs text-gray-500">
            <div className="mb-2 md:mb-0">
              Todos los derechos reservados{" "}
              <span className="text-secondary-red font-semibold">Sieger®</span>
            </div>
            <div className="flex space-x-4">
              <a
                href="#"
                className="hover:text-secondary-red transition-colors duration-200 "
              >
                Términos y condiciones
              </a>
              <span>|</span>
              <a
                href="#"
                className="hover:text-secondary-red transition-colors duration-200 "
              >
                Políticas de privacidad
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};
export default Footer;
// <div className="flex h-full w-full flex-col items-center justify-center gap-4 bg-white px-10 pt-4 md:px-24">
//       <div className="relative flex w-full flex-col items-center justify-between px-8 md:px-24 lg:flex-row lg:items-start">
//         <div className="order-1 flex flex-col items-center justify-center lg:order-2 lg:pt-6">
//           {/* <picture className="relative left-2 md:left-0">
//             <source
//               media="(min-width: 300px)"
//               srcSet={`${greetingsImg1x} 1x, ${greetingsImg2x} 2x, ${greetingsImg3x} 3x`}
//             />
//             <img src={greetingsImg1x} alt="¡Estamos en contacto!" />
//           </picture> */}
//         </div>
//         <div className="relative order-2 mt-3 pb-3 pt-5 text-center font-openSans text-black lg:order-3 lg:mt-0 lg:min-h-[209px]">
//           <p className="pb-2 text-base font-semibold">Redes sociales</p>
//           <div className="flex items-center justify-center gap-6">
//             <a
//               href="https://www.instagram.com/agilityoficial_/"
//               target="_blank"
//               rel="noopener noreferrer"
//               title="Instagram"
//             >
//               {/* <img
//                 src={instagramLogo}
//                 className="max-w-7"
//                 alt="Instagram Logo"
//               /> */}
//               insta
//             </a>
//             <a
//               href="https://open.spotify.com/user/31dvaczejhpzjtjm236ayyumzv2m"
//               target="_blank"
//               rel="noopener noreferrer"
//               title="Spotify"
//             >
//               {/* <img src={spotifyLogo} className="max-w-7" alt="Spotify Logo" /> */}
//               spoti
//             </a>
//             <a
//               href="https://www.facebook.com/agilityoficial"
//               target="_blank"
//               rel="noopener noreferrer"
//               title="Facebook"
//             >
//               {/* <img
//                 src={facebookLogo}
//                 className="max-w-7"
//                 alt="Facebook Logo"
//               /> */}
//               fcb
//             </a>
//           </div>
//         </div>
//         <div className="order-3 pt-5 text-center font-openSans text-white lg:order-1">
//           <p className="pb-2 text-base font-semibold">Accesos rápidos</p>
//           <ul className="flex list-none flex-col gap-2 text-base font-light md:gap-0.5">
//             <Link
//               to="/home"
//               state={{ scrollToId: "dog-discover-products" }}
//               className="cursor-pointer transition-all duration-100 hover:underline"
//             >
//               <li>Agility Perro</li>
//             </Link>
//             <Link
//               to="/home"
//               state={{ scrollToId: "cat-products" }}
//               className="cursor-pointer transition-all duration-100 hover:underline"
//             >
//               <li>Agility Gato</li>
//             </Link>
//             <Link
//               to="/plus/perro"
//               state={{ scrollToId: "agility-plus-dog-products" }}
//               className="cursor-pointer transition-all duration-100 hover:underline"
//             >
//               <li className="cursor-pointer transition-all duration-100 hover:underline">
//                 Agility+ Perro
//               </li>
//             </Link>
//             <Link
//               to="/plus/gato"
//               state={{ scrollToId: "agility-plus-cat-products" }}
//               className="cursor-pointer transition-all duration-100 hover:underline"
//             >
//               <li className="cursor-pointer transition-all duration-100 hover:underline">
//                 Agility+ Gato
//               </li>
//             </Link>
//             {/* <Link to="/donde-comprar" className="cursor-pointer">
//               <li className="cursor-pointer transition-all duration-100 hover:underline">
//                 Dónde comprar
//               </li>
//             </Link> */}
//             {/* <Link to="/blog" className="cursor-pointer">
//               <li className="cursor-pointer transition-all duration-100 hover:underline">
//                 Blog
//               </li>
//             </Link> */}
//             <li className="cursor-pointer transition-all duration-100 hover:underline">
//               Contacto
//             </li>
//           </ul>
//         </div>
//         {/* <picture className="absolute -bottom-2 right-0 lg:right-[5.7rem]">
//           <source
//             media="(min-width: 300px)"
//             srcSet={`${logoAlican1x} 1x, ${logoAlican2x} 2x, ${logoAlican3x} 3x`}
//           />
//           <img src={logoAlican1x} alt="Greetings image" />
//         </picture> */}
//       </div>
//       <div className="m-auto block w-full border-t-2 border-t-gray-500">
//         <p className="py-2 pb-4 text-center font-openSans text-xs font-light text-white">
//           © Copyright 2025. Agility Todos los derechos reservados.
//         </p>
//       </div>
//     </div>
