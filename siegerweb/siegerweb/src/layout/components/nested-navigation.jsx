import React, { useMemo, useState } from "react";
import chevronRight from "@/assets/icons/chevron_right.webp";
import closeIcon from "@/assets/icons/close.webp";
import { useNavbar } from "@/stores/use-navbar";
import { clx } from "@/utils/clx";
import { menuData, mainCategories } from "@/utils/menu-labels";
import { Link } from "react-router-dom";
import useDevice from "../../hooks/use-device";

const NestedNavigation = () => {
  const { isMobile } = useDevice();

  const currentMenu = useMemo(() => {
    if (!isMobile) return menuData;

    return {
      ...menuData,
    };
  }, [isMobile]);

  const [navigationStack, setNavigationStack] = useState([
    { data: currentMenu, title: "  " },
  ]);
  const [navigationState, setNavigationState] = useState("idle");
  const [direction, setDirection] = useState("forward");
  const setIsMenuOpen = useNavbar((state) => state.setIsMenuOpen);

  const handleBack = () => {
    if (navigationStack.length > 1) {
      setDirection("backward");
      setNavigationState("transitioning");
      setTimeout(() => {
        setNavigationStack((prev) => prev.slice(0, -1));
        setNavigationState("idle");
      }, 300);
    } else {
      setIsMenuOpen(false);
    }
  };

  const handleNavigate = (subMenu, title) => {
    if (subMenu) {
      setDirection("forward");

      const isItemsArray =
        Array.isArray(subMenu) && subMenu.length > 0 && "name" in subMenu[0];

      setNavigationStack((prev) => [
        ...prev,
        {
          data: isItemsArray ? { finalItems: subMenu } : subMenu,
          title,
          isFinalLevel: isItemsArray,
        },
      ]);

      setNavigationState("transitioning");
      setTimeout(() => {
        setNavigationState("idle");
      }, 300);
    } else {
      setIsMenuOpen(false);
    }
  };

  const currentLevel = navigationStack[navigationStack.length - 1];
  const previousLevel = navigationStack[navigationStack.length - 2];

  const getAnimationClass = () => {
    if (navigationState === "transitioning") {
      return direction === "forward"
        ? "animate-slide-in-right"
        : "animate-slide-out-left";
    }
    return "";
  };

  const renderFinalItems = (items) => {
    return items.map((item, index) => (
      <Link
        to={`${item.slug}`}
        key={`${item.id}-${index}`}
        onClick={() => setIsMenuOpen(false)}
      >
        <li className="mb-1 flex w-full cursor-pointer items-center justify-between border-b border-gray-200 p-2 py-3">
          <p className="pl-1">{item.name}</p>
        </li>
      </Link>
    ));
  };

  const renderItems = (items) => {
    if (!items) return null;

    if (items.finalItems) {
      return renderFinalItems(items.finalItems);
    }

    return Object.entries(items).map(([key, value]) => {
      if (typeof value === "string") {
        return (
          <li
            key={key}
            className="mb-1 flex w-full cursor-pointer items-center justify-between border-b border-gray-200 p-2 py-3"
          >
            <p className="pl-1">{value}</p>
          </li>
        );
      }

      if (Array.isArray(value)) {
        if (
          value.length > 0 &&
          typeof value[0] === "object" &&
          "name" in value[0]
        ) {
          return renderFinalItems(value);
        }

        return value.map((item, index) => (
          <li
            key={`${key}-${index}`}
            className="mb-1 flex w-full cursor-pointer items-center justify-between border-b border-gray-200 p-2 py-3"
          >
            <p className="pl-1">{item}</p>
          </li>
        ));
      }

      if (value.items) {
        return (
          <li
            key={key}
            onClick={() => handleNavigate(value.items, value.title)}
            className="mb-1 flex w-full cursor-pointer items-center justify-between border-b border-gray-200 p-2 py-3"
          >
            <p className="pl-1">{value.title}</p>
            <img src={chevronRight} alt="Right Arrow" className="h-6 w-6" />
          </li>
        );
      }

      return (
        <li
          key={key}
          onClick={() =>
            handleNavigate(value.subMenu || value.items, value.title)
          }
          className="mb-1 flex w-full cursor-pointer items-center justify-between border-b border-gray-200 p-2 py-3"
        >
          <Link
            to={value.route || "#"}
            className="flex w-full items-center justify-between"
          >
            <p className="pl-1">{value.title}</p>
            {(value.subMenu || value.items) && (
              <img src={chevronRight} alt="Right Arrow" className="h-6 w-6" />
            )}
          </Link>
        </li>
      );
    });
  };

  const getPreviousTitle = () => {
    if (
      navigationStack.length === 2 &&
      mainCategories.includes(currentLevel.title)
    ) {
      return "Todo";
    }
    return previousLevel?.title || "";
  };

  return (
    <div className={`flex h-full w-full flex-col ${getAnimationClass()}`}>
      <div className="mb-2 flex flex-col">
        <div
          className={clx(
            "mb-2 flex",
            navigationStack.length > 1 ? "justify-start" : "justify-end"
          )}
        >
          <button
            onClick={handleBack}
            className="flex items-center justify-end text-neutrals-black"
          >
            {navigationStack.length > 1 ? (
              <img
                src={chevronRight}
                alt="Back"
                className="mr-1 h-6 w-6 rotate-180 transform"
              />
            ) : (
              <img
                src={closeIcon}
                alt="Back"
                className="mr-3 mt-1 h-3 w-3 rotate-180 transform"
              />
            )}

            <span className="text-base text-neutrals-gray">
              {getPreviousTitle()}
            </span>
          </button>
        </div>
        <h2 className="mt-2 pl-2 text-lg font-semibold text-neutrals-sandy">
          {currentLevel.title}
        </h2>
      </div>
      <ul className="w-full list-none">{renderItems(currentLevel.data)}</ul>
    </div>
  );
};

export default NestedNavigation;
