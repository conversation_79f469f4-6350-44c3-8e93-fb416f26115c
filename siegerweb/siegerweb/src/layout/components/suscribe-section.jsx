import { useEffect, useState } from "react";
import Suscribe<PERSON>utton from "./suscribe-button";
import { useSubscribeNewsletter } from "@/hooks/queries/use-suscribe-newsletter";
import { useSuscribePP } from "@/stores/use-suscribe-pp";
import Shape1x from "@/assets/images/footer/shape.webp";
import Shape2x from "@/assets/images/footer/<EMAIL>";
import Shape3x from "@/assets/images/footer/<EMAIL>";
import Animales1x from "@/assets/images/footer/animales.webp";
import Animales2x from "@/assets/images/footer/<EMAIL>";
import Animales3x from "@/assets/images/footer/<EMAIL>";
import { clx } from "@/utils/clx";

const SuscribeSection = () => {
  const { isPP } = useSuscribePP();
  const [pet, setPet] = useState("Perro");
  const [email, setEmail] = useState("");
  const [showMessage, setShowMessage] = useState(false);
  const { mutate, isLoading, isSuccess, isError, _error } =
    useSubscribeNewsletter();

  const handleSubmit = (e) => {
    e.preventDefault();
    console.log({ email, listID: import.meta.env.VITE_LIST_ID_MAILCHIMP, pet });
    mutate({ email, listID: import.meta.env.VITE_LIST_ID_MAILCHIMP, pet });
  };

  useEffect(() => {
    if (isSuccess || isError) {
      setShowMessage(true);
      // ocultarlo después de 5 segundos
      const timer = setTimeout(() => {
        setShowMessage(false);
      }, 5000); // 5000 ms = 5 segundos
      // Limpiar el temporizador cuando el componente se desmonte o el efecto se vuelva a ejecutar
      return () => clearTimeout(timer);
    }
  }, [isSuccess, isError]);

  return (
    <div className="relative flex w-full flex-col items-center justify-start gap-8 overflow-hidden bg-white px-0 md:px-4 lg:pl-0 pt-10 pb-5 md:pt-14 lg:flex-row lg:gap-0">
      <div className="lg:ml-14 px-2 md:px-0">
        <h3 className="text-center font-gothamBold text-3xl lg:text-5xl lg:text-left ">
          Suscribete a nuestro
          <span className="text-secondary-red"> Newsletter</span>
        </h3>
        <div className="mt-2 md:mt-7 lg:mt-3 flex flex-col md:flex-row gap-4">
          <p className="text-center md:text-left text-sm text-gray-500 font-corbert font-light lg:text-left">
            Recibí consejos, novedades y promociones exclusivas para disfrutar
            al máximo con tu mascota.
          </p>
          <div className="flex flex-col items-start justify-center xl:justify-start">
            <form
              onSubmit={handleSubmit}
              className="w-full relative lg:pr-6 mt-2 md:mt-0 md:place-self-start flex items-center justify-center bg-white gap-1 font-poppinsLight"
            >
              <div className="flex rounded-md border border-gray-200 w-full">
                {/* <select
                name="pet"
                id="pet"
                className="max-w-[25%] rounded-full bg-white pr-2 focus:border-0 focus:outline-none"
                value={pet}
                onChange={(e) => {
                  setPet(e.target.value);
                }}
              >
                <option value="Perro" defaultValue>
                  Perro
                </option>
                <option value="Gato">Gato</option>
                <option value="Perro y Gato">Perro y Gato</option>
              </select> */}
                <input
                  type="email"
                  name="email"
                  id="email"
                  required
                  value={email}
                  onChange={(e) => {
                    setEmail(e.target.value);
                  }}
                  placeholder="Ingresá tu email"
                  className="min-w-[200px] xl:min-w-[250px] text-xs rounded-full px-2 py-2 focus:border-0 focus:outline-none"
                />
              </div>
              <SuscribeButton isLoading={isLoading} />
              <p className="font-corbert absolute left-1/2 w-full -translate-x-1/2 text-left text-sm -translate-y-9 ml-1">
                {showMessage && (
                  <>
                    {isSuccess && "¡Gracias por suscribirte!"}
                    {isError && (
                      <span className="text-secondary-red">
                        Error al suscribirse!
                      </span>
                    )}
                  </>
                )}
              </p>
            </form>
            <p className="font-corbert  lg:ml-1 w-full text-left text-[10px] 2xl:text-sm">
              Protegemos tus datos personales.{" "}
              <a
                href="#"
                target="_blank"
                className="text-secondary-red hover:underline"
              >
                Política de privacidad
              </a>
            </p>
          </div>
        </div>
      </div>
      <div className="block md:hidden lg:block relative max-w-[450px]">
        <picture className="">
          <source
            media="(min-width: 300px)"
            srcSet={`${Animales1x} 1x, ${Animales2x} 2x, ${Animales3x} 3x`}
          />
          <img
            src={Animales1x}
            alt="Perro y gato"
            className="object-contain -ml-9 md:ml-0"
          />
        </picture>
      </div>
    </div>
  );
};
export default SuscribeSection;
