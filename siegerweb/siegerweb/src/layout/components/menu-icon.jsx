import { useNavbar } from "@/stores/use-navbar";
import { Menu } from "lucide-react";

const MenuIcon = () => {
  const isMenuOpen = useNavbar((state) => state.isMenuOpen);

  return (
    <button
      title="Menu"
      aria-label={isMenuOpen ? "Close menu" : "Open menu"}
      aria-expanded={isMenuOpen}
      onClick={() => {
        useNavbar.setState({
          isMenuOpen: !isMenuOpen,
        });
      }}
      onKeyDown={(e) => {
        if (e.key === "Enter" || e.key === " ") {
          e.preventDefault();
          useNavbar.setState({
            isMenuOpen: !isMenuOpen,
          });
        }
      }}
      className="h-auto w-auto focus:outline-none focus:ring-2 focus:ring-primary-red focus:ring-offset-2 rounded-md p-1"
    >
      <Menu className=" relative -top-0.5 mr-1.5 aspect-square w-[1.25rem] object-contain" />
    </button>
  );
};

export default MenuIcon;
