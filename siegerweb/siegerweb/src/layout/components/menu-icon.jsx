import { useNavbar } from "@/stores/use-navbar";
import { Menu } from "lucide-react";

const MenuIcon = () => {
  const isMenuOpen = useNavbar((state) => state.isMenuOpen);

  return (
    <button
      title="Menu"
      onClick={() => {
        useNavbar.setState({
          isMenuOpen: !isMenuOpen,
        });
      }}
      className="h-auto w-auto"
    >
      <Menu className=" relative -top-0.5 mr-1.5 aspect-square w-[1.25rem] object-contain" />
    </button>
  );
};

export default MenuIcon;
