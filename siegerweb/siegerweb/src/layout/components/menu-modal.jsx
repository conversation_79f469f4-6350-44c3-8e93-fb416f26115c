import React, { useState, useEffect, useRef } from "react";
import { clx } from "@/utils/clx";
import { menuData } from "@/utils/menu-labels";
import { Link } from "react-router-dom";
import { ChevronDown, ChevronRight, ChevronRightCircle } from "lucide-react";

// perros
import puppyImg from "@/assets/images/menu/puppy.webp";
import puppyImg2x from "@/assets/images/menu/<EMAIL>";
import puppyImg3x from "@/assets/images/menu/<EMAIL>";
import dogAdultImg from "@/assets/images/menu/dogAdult.webp";
import dogAdultImg2x from "@/assets/images/menu/<EMAIL>";
import dogAdultImg3x from "@/assets/images/menu/<EMAIL>";
import dogSeniorImg from "@/assets/images/menu/dogSenior.webp";
import dogSeniorImg2x from "@/assets/images/menu/<EMAIL>";
import dogSeniorImg3x from "@/assets/images/menu/<EMAIL>";
import dogCriadoresImg from "@/assets/images/menu/dogCriadores.webp";
import dogCriadoresImg2x from "@/assets/images/menu/<EMAIL>";
import dogCriadoresImg3x from "@/assets/images/menu/<EMAIL>";
import dogEspecialesImg from "@/assets/images/menu/dogEspeciales.webp";
import dogEspecialesImg2x from "@/assets/images/menu/<EMAIL>";
import dogEspecialesImg3x from "@/assets/images/menu/<EMAIL>";

// gatos
import kittenImg from "@/assets/images/menu/kitten.webp";
import kittenImg2x from "@/assets/images/menu/<EMAIL>";
import kittenImg3x from "@/assets/images/menu/<EMAIL>";
import catAdultImg from "@/assets/images/menu/catAdult.webp";
import catAdultImg2x from "@/assets/images/menu/<EMAIL>";
import catAdultImg3x from "@/assets/images/menu/<EMAIL>";
import catSeniorImg from "@/assets/images/menu/catSenior.webp";
import catSeniorImg2x from "@/assets/images/menu/<EMAIL>";
import catSeniorImg3x from "@/assets/images/menu/<EMAIL>";
import catCriadoresImg from "@/assets/images/menu/catCriadores.webp";
import catCriadoresImg2x from "@/assets/images/menu/<EMAIL>";
import catCriadoresImg3x from "@/assets/images/menu/<EMAIL>";
import catEspecialesImg from "@/assets/images/menu/catEspeciales.webp";
import catEspecialesImg2x from "@/assets/images/menu/<EMAIL>";
import catEspecialesImg3x from "@/assets/images/menu/<EMAIL>";

const menuImages = {
  // Perros
  puppy: { img1x: puppyImg, img2x: puppyImg2x, img3x: puppyImg3x },
  dogAdult: { img1x: dogAdultImg, img2x: dogAdultImg2x, img3x: dogAdultImg3x },
  dogSenior: {
    img1x: dogSeniorImg,
    img2x: dogSeniorImg2x,
    img3x: dogSeniorImg3x,
  },
  dogCriadores: {
    img1x: dogCriadoresImg,
    img2x: dogCriadoresImg2x,
    img3x: dogCriadoresImg3x,
  },
  dogEspeciales: {
    img1x: dogEspecialesImg,
    img2x: dogEspecialesImg2x,
    img3x: dogEspecialesImg3x,
  },

  // Gatos
  kitten: { img1x: kittenImg, img2x: kittenImg2x, img3x: kittenImg3x },
  catAdult: { img1x: catAdultImg, img2x: catAdultImg2x, img3x: catAdultImg3x },
  catSenior: {
    img1x: catSeniorImg,
    img2x: catSeniorImg2x,
    img3x: catSeniorImg3x,
  },
  catCriadores: {
    img1x: catCriadoresImg,
    img2x: catCriadoresImg2x,
    img3x: catCriadoresImg3x,
  },
  catEspeciales: {
    img1x: catEspecialesImg,
    img2x: catEspecialesImg2x,
    img3x: catEspecialesImg3x,
  },
};

const MenuModal = ({ isOpen, onClose, category }) => {
  const [hoveredItem, setHoveredItem] = useState(null);
  const [isScrolled, setIsScrolled] = useState(false);
  const menuContent = menuData[category]?.subMenu || {};
  const modalRef = useRef(null);

  // Handle scroll to keep modal at top when scrolled
  useEffect(() => {
    const handleScroll = () => {
      const scrollTop =
        window.pageYOffset || document.documentElement.scrollTop;
      setIsScrolled(scrollTop > 0);
    };

    if (isOpen) {
      window.addEventListener("scroll", handleScroll);
      handleScroll(); // Check initial scroll position
    }

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, [isOpen]);

  // Handle keyboard navigation and focus management
  useEffect(() => {
    if (!isOpen) return;

    const handleKeyDown = (e) => {
      if (e.key === "Escape") {
        onClose();
        return;
      }

      if (e.key === "Tab") {
        const focusableElements = modalRef.current?.querySelectorAll(
          'a[href], button:not([disabled]), [tabindex]:not([tabindex="-1"])'
        );

        if (!focusableElements?.length) return;

        const firstElement = focusableElements[0];
        const lastElement = focusableElements[focusableElements.length - 1];

        if (e.shiftKey) {
          // Shift + Tab
          if (document.activeElement === firstElement) {
            e.preventDefault();
            lastElement.focus();
          }
        } else {
          // Tab
          if (document.activeElement === lastElement) {
            e.preventDefault();
            firstElement.focus();
          }
        }
      }
    };

    document.addEventListener("keydown", handleKeyDown);

    // Focus first focusable element when modal opens
    setTimeout(() => {
      const firstFocusable = modalRef.current?.querySelector(
        'a[href], button:not([disabled]), [tabindex]:not([tabindex="-1"])'
      );
      if (firstFocusable) {
        firstFocusable.focus();
      }
    }, 100);

    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [isOpen, onClose]);

  const getMenuImages = (itemId) => {
    return menuImages[itemId] || null;
  };

  const renderMenuContent = (content) => {
    if (!content) return null;

    return (
      <div className="grid grid-cols-5 w-full">
        {Object.entries(content).map(([key, section]) => (
          <div
            key={key}
            className="space-y-2 text-sm text-neutrals-gray min-w-[210px] mr-2"
          >
            <h3 className="text-base font-gothamBold text-black">
              {section.title}
            </h3>
            <ul className="">
              {section.items?.map((item) => (
                <li
                  key={item.id}
                  className="group py-1 hover:bg-red-100 focus-within:bg-red-100 group transition-colors duration-200 pl-1 rounded-md cursor-pointer font-corbert"
                  onMouseEnter={() => {
                    console.log("Hover en item:", item.id); // Debug
                    setHoveredItem(item.id);
                  }}
                  onMouseLeave={() => setHoveredItem(null)}
                >
                  <Link
                    to={`${item.slug}`}
                    className="transition-colors duration-200 flex items-center justify-between focus:outline-none  rounded-md px-2 py-1"
                    onFocus={() => setHoveredItem(item.id)}
                    onBlur={() => setHoveredItem(null)}
                    onKeyDown={(e) => {
                      if (e.key === "Enter" || e.key === " ") {
                        e.preventDefault();
                        e.currentTarget.click();
                      }
                    }}
                  >
                    {item.name}
                    <ChevronRight
                      size={16}
                      className={clx(
                        " opacity-0 group-hover:opacity-100 group-focus-within:opacity-100 transition-opacity duration-200"
                      )}
                    />
                  </Link>
                </li>
              ))}
            </ul>

            <Link
              to="/productos"
              className="group mt-4 flex items-center gap-1 font-gothamMedium text-black hover:underline focus:outline-none  rounded-md px-2 py-1 max-w-fit"
              onKeyDown={(e) => {
                if (e.key === "Enter" || e.key === " ") {
                  e.preventDefault();
                  e.currentTarget.click();
                }
              }}
            >
              Ver todos <ChevronRight size={16} className="text-primary-red" />
            </Link>
          </div>
        ))}

        <div className="col-span-1 space-y-4 w-60 text-black text-sm font-gotham">
          <Link
            to="/calcula-tu-porcion"
            className="flex items-center justify-start gap-1 focus:outline-none  rounded-md px-2 py-1"
            onKeyDown={(e) => {
              if (e.key === "Enter" || e.key === " ") {
                e.preventDefault();
                e.currentTarget.click();
              }
            }}
          >
            <div className="flex items-center gap-1">
              <ChevronRight size={18} className="text-primary-red" />
              <p className="hover:text-primary-red transition-colors duration-200">
                Calcula tu porción
              </p>
              <span className="rounded-full bg-red-100 px-2 py-0.5 text-xs font-gothamMedium text-primary-green">
                NUEVO
              </span>
            </div>
          </Link>
          <Link
            to="/sieger-vet"
            className="flex items-center justify-start gap-1 focus:outline-none  rounded-md px-2 py-1"
            onKeyDown={(e) => {
              if (e.key === "Enter" || e.key === " ") {
                e.preventDefault();
                e.currentTarget.click();
              }
            }}
          >
            <div className="flex items-center gap-1">
              <ChevronRight size={18} className="text-primary-red" />

              <p className="hover:text-primary-red transition-colors duration-200">
                Sieger Vet
              </p>
              <span className="rounded-full bg-red-100 px-2 py-0.5 text-xs font-gothamMedium text-primary-blackie">
                PRONTO
              </span>
            </div>
          </Link>
        </div>
        {/* IMAGENES */}
        <div className="col-span-2 space-y-4 ml-10 text-black text-sm font-gotham rounded-md p-4">
          {hoveredItem && getMenuImages(hoveredItem) && (
            <div className="relative">
              <picture className="">
                <source
                  media="(min-width: 300px)"
                  srcSet={`${getMenuImages(hoveredItem).img1x} 1x, ${
                    getMenuImages(hoveredItem).img2x
                  } 2x, ${getMenuImages(hoveredItem).img3x} 3x`}
                />
                <img
                  src={getMenuImages(hoveredItem).img1x}
                  alt={hoveredItem}
                  className={clx(
                    "w-full max-h-[185px] object-contain rounded-md",
                    hoveredItem === "catEspeciales" && "scale-125"
                  )}
                />
              </picture>

              {/* overlay */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-black/20 to-transparent rounded-md"></div>

              {/* img title */}
              <div className="absolute bottom-1 left-4 right-4">
                <h3 className="text-white text-lg text-center font-gothamMedium px-5">
                  Sieger{" "}
                  {hoveredItem === "puppy"
                    ? "Sieger Puppy"
                    : hoveredItem === "dogAdult"
                    ? "Sieger Adult"
                    : hoveredItem === "dogSenior"
                    ? "Sieger Senior"
                    : hoveredItem === "kitten"
                    ? "Sieger Katze Kitten"
                    : hoveredItem === "catAdult"
                    ? "Sieger Katze Adult"
                    : hoveredItem === "catSenior"
                    ? "Sieger Katze Senior"
                    : hoveredItem === "dogCriadores"
                    ? "Sieger Criadores"
                    : hoveredItem === "catCriadores"
                    ? "Sieger Katze Criadores"
                    : hoveredItem === "dogEspeciales"
                    ? "Sieger Necesidades Especiales"
                    : hoveredItem === "catEspeciales"
                    ? "Sieger Katze Necesidades Especiales"
                    : ""}
                </h3>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div
      ref={modalRef}
      className={clx(
        "fixed left-0 right-0 z-50 transition-all duration-200 ease-linear",
        isScrolled ? "top-0" : "top-16",
        isOpen ? "visible opacity-100" : "invisible opacity-0"
      )}
      role="dialog"
      aria-modal="true"
      aria-label="Navigation menu"
      onClick={onClose}
    >
      <div
        className={clx(
          "bg-white w-full max-w-5xl mx-auto ml-[70px] transform transition-all duration-200 ease-linear shadow-xl",
          isOpen
            ? "translate-y-0 opacity-100 rounded-b-lg"
            : "-translate-y-4 opacity-0"
        )}
        onClick={(e) => e.stopPropagation()}
      >
        <div className="z-50 bg-white rounded-lg overflow-hidden">
          <div className="pl-4 pr-2 py-3 w-full shadow-md shadow-gray-300 rounded-lg">
            <div className={clx("")}>{renderMenuContent(menuContent)}</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MenuModal;
