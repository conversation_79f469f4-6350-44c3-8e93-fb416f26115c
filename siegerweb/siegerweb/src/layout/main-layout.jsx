import { useState, useEffect } from "react";
import { Link, Outlet } from "react-router-dom";
import { useNavbar } from "@/stores/use-navbar";
import NestedNavigation from "./components/nested-navigation";
import MenuModal from "./components/menu-modal";
import useDevice from "@/hooks/use-device";
import { menuData } from "@/utils/menu-labels";
import { clx } from "@/utils/clx";
import siegerLogo1x from "@/assets/icons/logo.webp";
import siegerLogo2x from "@/assets/icons/logo.webp";
import siegerLogo3x from "@/assets/icons/logo.webp";
import MenuIcon from "./components/menu-icon";
import Footer from "./components/footer";
import { ChevronDown, SearchIcon } from "lucide-react";
import SiegerVetButton from "@/assets/images/siegervet-button.webp";

export const MainLayout = () => {
  const isMenuOpen = useNavbar((state) => state.isMenuOpen);
  const { isMobile, isTablet } = useDevice();
  const [activeMenu, setActiveMenu] = useState(null);
  const [menuState, setMenuState] = useState("closed");
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
  }, []);
  useEffect(() => {
    if (isMenuOpen) {
      setMenuState("opening");
      const timer = setTimeout(() => {
        setMenuState("open");
      }, 300);
      return () => clearTimeout(timer);
    } else if (menuState === "open") {
      setMenuState("closing");
      const timer = setTimeout(() => {
        setMenuState("closed");
      }, 300);
      return () => clearTimeout(timer);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isMenuOpen]);

  const getMenuAnimationClass = () => {
    switch (menuState) {
      case "opening":
        return "animate-slide-in-right";
      case "closing":
        return "animate-slide-out-left";
      default:
        return "";
    }
  };

  const handleMenuClick = (key) => {
    if (menuData[key]?.subMenu) {
      // If the same menu is already active, close it; otherwise, open the new menu
      setActiveMenu(activeMenu === key ? null : key);
    }
  };

  const handleCloseMenu = () => {
    setActiveMenu(null);
  };

  return (
    <div className="relative flex min-h-screen flex-col">
      <nav
        className={clx(
          "absolute left-0 font-gotham text-sm sm:text-base top-0 z-50 flex w-full items-center justify-center bg-transparent p-2 transition-all duration-700 ease-in-out bg-white shadow-gray-500 shadow-md ",
          isVisible
            ? "translate-y-0 opacity-100"
            : "translate-y-[-5px] opacity-0"
        )}
      >
        {isMobile || isTablet ? (
          <div className="flex items-center justify-between w-full">
            <Link to="/">
              <picture className="w-auto">
                <source
                  media="(min-width: 300px)"
                  srcSet={`${siegerLogo1x} 1x, ${siegerLogo2x} 2x, ${siegerLogo3x} 3x`}
                />
                <img src={siegerLogo1x} alt="Logo Sieger" className="w-12" />
              </picture>
            </Link>
            <MenuIcon />
            {menuState !== "closed" && (
              <div className="fixed left-0 top-0 z-50 flex h-screen w-full items-center justify-start bg-black/50">
                <div
                  className={`flex h-full w-full items-start justify-start bg-white p-4 ${getMenuAnimationClass()}`}
                >
                  <NestedNavigation />
                </div>
              </div>
            )}
          </div>
        ) : (
          // isDesktop
          <>
            <div className="flex w-full items-center justify-between py-1 px-3">
              <div className="flex gap-5 items-center justify-start">
                <Link to="/">
                  <picture className="w-auto">
                    <source
                      media="(min-width: 300px)"
                      srcSet={`${siegerLogo1x} 1x, ${siegerLogo2x} 2x, ${siegerLogo3x} 3x`}
                    />
                    <img
                      src={siegerLogo1x}
                      alt="Logo Sieger"
                      className="w-12"
                    />
                  </picture>
                </Link>
                <ul className="flex list-none flex-row gap-6">
                  {Object.entries(menuData).map(([key, value]) => (
                    <li key={key}>
                      {key === "Productos" ? (
                        <button
                          className={clx(
                            "flex gap-1 items-center justify-center cursor-pointer transition-all duration-200 hover:text-primary-red focus:outline-none  rounded-md px-2 py-1",
                            activeMenu === key ? "text-primary-red" : ""
                          )}
                          onClick={() => handleMenuClick(key)}
                          onKeyDown={(e) => {
                            if (e.key === "Enter" || e.key === " ") {
                              e.preventDefault();
                              handleMenuClick(key);
                            }
                          }}
                          aria-expanded={activeMenu === key}
                          aria-haspopup="true"
                        >
                          {value.title}
                          <ChevronDown
                            size={20}
                            className={clx(
                              "mt-1 text-gray-400 transition-transform duration-300",
                              activeMenu === key ? "rotate-180" : ""
                            )}
                          />
                        </button>
                      ) : (
                        <Link
                          to={value.route}
                          className={clx(
                            "flex gap-1 items-center justify-center cursor-pointer transition-all duration-200 hover:text-primary-red focus:outline-none  rounded-md px-2 py-1"
                          )}
                          onKeyDown={(e) => {
                            if (e.key === "Enter" || e.key === " ") {
                              e.preventDefault();
                              e.currentTarget.click();
                            }
                          }}
                        >
                          {value.title}
                        </Link>
                      )}
                    </li>
                  ))}
                </ul>
              </div>

              <div className="flex items-center justify-center gap-2">
                <button
                  aria-label="Search"
                  className="focus:outline-none  rounded-md p-1"
                  onKeyDown={(e) => {
                    if (e.key === "Enter" || e.key === " ") {
                      e.preventDefault();
                      // Add search functionality here
                    }
                  }}
                >
                  <SearchIcon size={20} className="text-black mr-2" />
                </button>
                <Link to="/calcula-tu-porcion">
                  <button
                    className="text-white text-sm px-2.5 py-1.5 bg-secondary-red hover:brightness-105 focus:outline-none focus:ring-2 focus:ring-secondary-red focus:ring-offset-2 transition-all duration-200 rounded-md"
                    onKeyDown={(e) => {
                      if (e.key === "Enter" || e.key === " ") {
                        e.preventDefault();
                        e.currentTarget.click();
                      }
                    }}
                  >
                    Calcula tu porción
                  </button>
                </Link>
                <Link to="">
                  {/* todo: confirmar a dónde dirige */}
                  <button
                    className="text-secondary-red border text-sm border-secondary-red px-4 transition-all py-1.5 hover:text-transparent focus:outline-none focus:ring-2 focus:ring-secondary-red focus:ring-offset-2 rounded-md bg-center"
                    style={{
                      "--bg-image": `url(${SiegerVetButton})`,
                    }}
                    onMouseEnter={(e) =>
                      (e.target.style.backgroundImage = `url(${SiegerVetButton})`)
                    }
                    onMouseLeave={(e) =>
                      (e.target.style.backgroundImage = "none")
                    }
                    onKeyDown={(e) => {
                      if (e.key === "Enter" || e.key === " ") {
                        e.preventDefault();
                        e.currentTarget.click();
                      }
                    }}
                  >
                    Sieger Vet
                  </button>
                </Link>
              </div>
            </div>
          </>
        )}
      </nav>
      <MenuModal
        isOpen={!!activeMenu}
        onClose={handleCloseMenu}
        category={activeMenu}
      />
      <div className=" overflow-x-hidden mt-12 ">
        <Outlet />
      </div>
      <Footer />
    </div>
  );
};
