import { axiosInstance } from "../axios";

export const getBanners = async () => {
  try {
    const requestBody = {
      type: "web",
      method: "banners",
      accion: "vigentes",
      website: 1, // id web Sieger
      limit: 5,
    };

    const response = await axiosInstance.post("/fetch/getdata", requestBody);
    return response.data;
  } catch (error) {
    console.error("Error al obtener banners:", error);
    if (error.response) {
      console.error("Detalles del error:", error.response.data);
      console.error("Status del error:", error.response.status);
      console.error("Headers del error:", error.response.headers);
    }
    return [];
  }
};
