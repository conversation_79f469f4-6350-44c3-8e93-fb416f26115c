import { axiosInstance } from "../axios";

// Obtener notas
export const getAllNotes = async () => {
  try {
    const requestBody = {
      type: "web",
      method: "blog",
      accion: "ver",
      website: "agilitypets.com",
    };

    const response = await axiosInstance.post("/fetch/getdata", requestBody);
    return response.data;
  } catch (error) {
    console.error("Error fetching notes:", error);
    if (error.response) {
      console.error("Error details:", error.response.data);
      console.error("Status:", error.response.status);
      console.error("Headers:", error.response.headers);
    }
    return [];
  }
};

// Obtener nota por ID
export const getNoteById = async (id) => {
  try {
    const requestBody = {
      type: "web",
      method: "blog",
      accion: "ver",
      id: id,
    };

    const response = await axiosInstance.post("/fetch/getdata", requestBody);
    return response.data;
  } catch (error) {
    console.error("Error fetching note:", error);
    if (error.response) {
      console.error("Error details:", error.response.data);
      console.error("Status:", error.response.status);
      console.error("Headers:", error.response.headers);
    }
    return [];
  }
};

// Obtener últimas notas
export const getLastNotes = async () => {
  try {
    const requestBody = {
      type: "web",
      method: "blog",
      accion: "ultimas",
      website: "agilitypets.com",
      limit: 3,
    };

    const response = await axiosInstance.post("/fetch/getdata", requestBody);

    return response.data;
  } catch (error) {
    console.error("Error fetching note:", error);
    if (error.response) {
      console.error("Error details:", error.response.data);
      console.error("Status:", error.response.status);
      console.error("Headers:", error.response.headers);
    }
    return [];
  }
};
