import { axiosInstance } from "../axios";

export const getPdvById = async (pdvId) => {
  try {
    const requestBody = {
      type: "web",
      method: "pdv",
      accion: "get",
      id: pdvId,
    };

    const response = await axiosInstance.post("/fetch/getdata", requestBody);
    return response.data;
  } catch (error) {
    console.error("Error fetching PDV by ID:", error);
    if (error.response) {
      console.error("Details:", error.response.data);
      console.error("Status:", error.response.status);
    }
    return null;
  }
};

export const getPdvs = async (website) => {
  try {
    const requestBody = {
      type: "web",
      method: "pdv",
      accion: "listar",
      ...(website && { website }),
    };

    const response = await axiosInstance.post("/fetch/getdata", requestBody);
    return response.data;
  } catch (error) {
    console.error("Error fetching PDVs:", error);
    if (error.response) {
      console.error("Details:", error.response.data);
      console.error("Status:", error.response.status);
    }
    return [];
  }
};
