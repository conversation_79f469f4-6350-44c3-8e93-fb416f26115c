import { axiosInstance } from '../axios';

export const subscribeNewsletter = async ({ email, listID, pet }) => {
  try {
    const body = {
      type: 'web',
      method: 'newsletter',
      accion: 'suscribirse',
      email,
      listID,
      pet,
    };

    const response = await axiosInstance.post('/fetch/getdata', body);
    return response.data;
  } catch (error) {
    console.error('Error subscribing to newsletter:', error);
    if (error.response) {
      console.error('Details:', error.response.data);
      console.error('Status:', error.response.status);
    }
    throw error;
  }
};
