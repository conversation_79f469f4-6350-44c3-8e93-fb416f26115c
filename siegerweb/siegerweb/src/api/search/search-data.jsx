import { axiosInstance } from '../axios';

export const getSearchData = async (query) => {
  try {
    const requestBody = {
      type: 'web',
      method: 'search',
      website: 'agilitypets.com',
      marca: 'agility',
      query: query,
    };

    const response = await axiosInstance.post('/fetch/getdata', requestBody);
    return response.data;
  } catch (error) {
    console.error('Error al obtener el artículo', error);
    if (error.response) {
      console.error('Detalles del error:', error.response.data);
      console.error('Status del error:', error.response.status);
      console.error('Headers del error:', error.response.headers);
    }
    return null;
  }
};
