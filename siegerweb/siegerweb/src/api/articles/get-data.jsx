import { axiosInstance } from "../axios";

// Obtener articulos
export const getArticles = async () => {
  try {
    const requestBody = {
      type: "web",
      method: "articulos",
      accion: "listar",
      marca: "sieger",
    };

    const response = await axiosInstance.post("/fetch/getdata", requestBody);
    return response.data;
  } catch (error) {
    console.error("Error al obtener artículos:", error);
    if (error.response) {
      console.error("Detalles del error:", error.response.data);
      console.error("Status del error:", error.response.status);
      console.error("Headers del error:", error.response.headers);
    }
    return [];
  }
};

// Obtener articulos por ID
export const getArticleByID = async (id) => {
  try {
    const requestBody = {
      type: "web",
      method: "articulos",
      accion: "get",
      id: id,
    };

    const response = await axiosInstance.post("/fetch/getdata", requestBody);
    return response.data;
  } catch (error) {
    console.error("Error al obtener el artículo", error);
    if (error.response) {
      console.error("Detalles del error:", error.response.data);
      console.error("Status del error:", error.response.status);
      console.error("Headers del error:", error.response.headers);
    }
    return null;
  }
};

// Obtener articulos por marca
export const getArticlesByMark = async (especie, mark) => {
  try {
    const requestBody = {
      type: "web",
      method: "articulos",
      accion: "listar",
      marca: mark,
      especie: especie,
    };

    const response = await axiosInstance.post("/fetch/getdata", requestBody);
    return response.data;
  } catch (error) {
    console.error("Error al obtener el artículo", error);
    if (error.response) {
      console.error("Detalles del error:", error.response.data);
      console.error("Status del error:", error.response.status);
      console.error("Headers del error:", error.response.headers);
    }
    return null;
  }
};

// Filtrar articulos
export const getFilteredArticles = async (filters = {}) => {
  try {
    const requestBody = {
      type: "web",
      method: "articulos",
      accion: "filtrar",
      marca: "Sieger",
      ...filters,
    };
    console.log(filters);

    const response = await axiosInstance.post("/fetch/getdata", requestBody);
    return response.data;
  } catch (error) {
    console.error("Error al filtrar artículos:", error);
    return { items: [] };
  }
};
