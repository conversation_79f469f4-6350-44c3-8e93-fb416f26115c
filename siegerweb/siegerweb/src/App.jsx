import "./App.css";
import { MainLayout } from "./layout/main-layout.jsx";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { Home } from "./pages/home/<USER>";
import Contacto from "./pages/contacto/index.jsx";
import Nosotros from "./pages/nosotros/index.jsx";
import CalculaTuPorcion from "./pages/calcula-tu-porcion/index.jsx";
import Puppy from "./pages/perro/puppy.jsx";
import DogAdult from "./pages/perro/adult.jsx";
import DogSenior from "./pages/perro/senior.jsx";
import Kitten from "./pages/gato/kitten.jsx";
import CatAdult from "./pages/gato/adult.jsx";
import CatSenior from "./pages/gato/senior.jsx";
import Productos from "./pages/productos/index.jsx";
import ProductDetail from "./pages/alimento/[id].jsx";

function App() {
  return (
    <>
      <BrowserRouter
        future={{ v7_relativeSplatPath: true, v7_startTransition: true }}
      >
        <Routes>
          <Route path="/" element={<MainLayout />}>
            <Route index element={<Home />} />
            <Route path="contacto" element={<Contacto />} />
            <Route path="nosotros" element={<Nosotros />} />
            <Route path="calcula-tu-porcion" element={<CalculaTuPorcion />} />

            {/* LANDINGS */}
            {/* LANDINGS PERRO */}
            <Route path="perro">
              <Route index element={<Navigate to="puppy" replace />} />
              <Route path="puppy" element={<Puppy />} />
              <Route path="adult" element={<DogAdult />} />
              <Route path="senior" element={<DogSenior />} />
            </Route>
            {/* LANDINGS GATO */}
            <Route path="gato">
              <Route index element={<Navigate to="kitten" replace />} />
              <Route path="kitten" element={<Kitten />} />
              <Route path="adult" element={<CatAdult />} />
              <Route path="senior" element={<CatSenior />} />
            </Route>

            <Route path="productos" element={<Productos />} />

            <Route path="">
              <Route path=":id?/:especie?/:slug?" element={<ProductDetail />} />
            </Route>
          </Route>
        </Routes>
      </BrowserRouter>
    </>
  );
}

export default App;
