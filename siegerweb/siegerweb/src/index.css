@import "tailwindcss/base";
@import "tailwindcss/components";
@import "tailwindcss/utilities";

@font-face {
  font-family: "Corbert";
  src: url("/src/assets/fonts/Corbert-Regular.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "CorbertMedium";
  src: url("/src/assets/fonts/Corbert-Medium.otf") format("truetype");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "CorbertBold";
  src: url("/src/assets/fonts/Corbert-Bold.otf") format("truetype");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "Poppins";
  src: url("/src/assets/fonts/Poppins-Regular.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "PoppinsLight";
  src: url("/src/assets/fonts/Poppins-Light.ttf") format("truetype");
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "Gotham";
  src: url("/src/assets/fonts/Gotham-Regular.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "GothamMedium";
  src: url("/src/assets/fonts/Gotham-Medium.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "GothamBold";
  src: url("/src/assets/fonts/Gotham-Bold.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@layer base {
  html {
    overflow-x: hidden;
    font-family: system-ui, sans-serif;
    scroll-behavior: smooth;
  }

  /* MEDIAS */
  @media (min-width: 1440px) {
    html {
      font-size: 18px;
    }
  }

  @media (min-width: 1920px) {
    html {
      font-size: 20px;
    }
  }

  @media (min-width: 2560px) {
    html {
      font-size: 24px;
    }
  }

  @media (min-width: 3000px) {
    html {
      font-size: 36px;
    }
  }
}
