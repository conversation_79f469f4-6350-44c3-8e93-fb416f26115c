.perspective {
  perspective: 1000px;
}

.backface-hidden {
  backface-visibility: hidden;
}

.rotate-y-180 {
  transform: rotateY(180deg);
}

/* Custom Checkbox Styles */
.custom-checkbox-label {
  display: flex;
  align-items: center;
  width: fit-content;
  cursor: pointer;
}

.custom-checkbox-input {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

.custom-checkbox-box {
  width: 16px;
  height: 16px;
  border: 1px solid #ccc;
  border-radius: 4px;
  background: #fff;
  margin-right: 7px;
  transition: background 0.2s, border-color 0.2s;
  display: inline-block;
  position: relative;
}

.custom-checkbox-box.checked {
  background: #ef3340;
  border-color: #ef3340;
}

.custom-checkbox-box.checked::after {
  content: "";
  position: absolute;
  left: 4.5px;
  top: 1px;
  width: 5px;
  height: 10px;
  border: solid #fff;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}
