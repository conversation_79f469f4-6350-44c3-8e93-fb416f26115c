import React from "react";
import Pets1x from "@/assets/images/calculate/pets.webp";
import Pets2x from "@/assets/images/calculate/<EMAIL>";
import Pets3x from "@/assets/images/calculate/<EMAIL>";
import PetsPP1x from "@/assets/images/calculate/pets-pp.webp";
import PetsPP2x from "@/assets/images/calculate/<EMAIL>";
import PetsPP3x from "@/assets/images/calculate/<EMAIL>";
import ShapeHex1x from "@/assets/images/home/<USER>";
import ShapeHex2x from "@/assets/images/home/<USER>";
import ShapeHex3x from "@/assets/images/home/<USER>";
import { Link } from "react-router-dom";

const Steps = () => {
  return (
    <div>
      <h3 className="font-gothamMedium text-3xl text-center lg:text-left">
        Calculá la <span className="text-secondary-red">porción ideal</span>{" "}
        <br className="hidden lg:block xl:hidden" />
        para tu mascota.
      </h3>
      <p className="mt-3 text-sm text-black font-corbert max-w-xl px-2 lg:px-0">
        En <span className="font-bold">Sieger</span> valoramos la ciencia y
        precisión. Por eso, creamos esta calculadora, diseñada para ofrecerte la
        porción exacta de alimento que tu mascota necesita, basada en sus
        características únicas.
      </p>
      <div className="flex flex-col md:flex-row px-5 md:px-0 gap-3 lg:gap-10 mt-4">
        <div className="flex gap-1">
          <div className="relative">
            <picture className="">
              <source
                media="(min-width: 300px)"
                srcSet={`${ShapeHex1x} 1x, ${ShapeHex2x} 2x, ${ShapeHex3x} 3x`}
              />
              <img
                src={ShapeHex1x}
                alt="Perro y gato"
                className="max-w-[300px]"
              />
            </picture>
            <span className="font-gothamMedium text-white absolute inset-0 mt-3 ml-4 md:mt-2.5 lg:mt-2.5 lg:ml-3.5 xl:ml-3 xl:mt-2 text-base 2xl:mt-0.5 2xl:ml-2">
              1
            </span>
          </div>
          <div className="flex flex-col">
            <h3 className="text-xs font-gothamBold ">
              Ingresá los datos de tu mascota
            </h3>
            <p className="text-xs font-gotham">
              Edad, peso, tamaño y necesidades específicas.
            </p>
          </div>
        </div>
        <div className="flex gap-1">
          <div className="relative">
            <picture className="">
              <source
                media="(min-width: 300px)"
                srcSet={`${ShapeHex1x} 1x, ${ShapeHex2x} 2x, ${ShapeHex3x} 3x`}
              />
              <img
                src={ShapeHex1x}
                alt="Perro y gato"
                className="max-w-[300px]"
              />
            </picture>
            <span className="font-gothamMedium text-white absolute inset-0 mt-3 ml-3.5 md:mt-2.5 lg:mt-2.5 lg:ml-3.5 xl:ml-3 xl:mt-2 text-base 2xl:mt-0.5 2xl:ml-2">
              2
            </span>
          </div>
          <div className="flex flex-col">
            <h3 className="text-xs font-gothamBold ">
              Recibí su porción recomendada
            </h3>
            <p className="text-xs font-gotham">
              Calculamos la porción ideal para su máximo bienestar.
            </p>
          </div>
        </div>
      </div>
      <div className="w-full flex items-center justify-center lg:-left-10 mt-4 relative">
        <Link to="">
          <button className="font-gothamMedium rounded-full px-5 py-2 text-sm bg-secondary-red hover:brightness-105 transition-all duration-200 text-white shadow-md shadow-gray-400">
            CALCULAR
          </button>
        </Link>
      </div>
    </div>
  );
};

const CalculaTuPorcion = ({ isPP = false }) => {
  return (
    <div className="lg:pt-3 pb-10 w-full flex items-center justify-center">
      {/* card  */}
      <div className="p-2 pb-4 lg:pr-10 rounded-xl shadow-md mx-5 shadow-gray-400 flex flex-col md:flex-row items-center lg:items-start justify-start gap-2 md:gap-10">
        {/* img  */}
        <div className="">
          <picture className="">
            <source
              media="(min-width: 300px)"
              srcSet={(PetsPP1x, PetsPP2x, PetsPP3x)}
            />
            <img
              src={isPP ? PetsPP1x : Pets1x}
              alt="Perro y gato"
              className="max-w-[320px] lg:max-w-[300px]"
            />
          </picture>
        </div>
        <Steps />
      </div>
    </div>
  );
};

export default CalculaTuPorcion;
