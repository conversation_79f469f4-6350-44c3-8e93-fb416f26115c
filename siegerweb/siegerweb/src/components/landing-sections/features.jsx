import React from "react";
import { descriptionTexts } from "./description-texts";
import useDevice from "@/hooks/use-device";
import AccordionItem from "./accordion";
import Accordion from "./accordion";
import { useLocation } from "react-router-dom";
import { clx } from "../../utils/clx";

const Features = ({ beneficiosTitleImg, beneficiosImg, title, color }) => {
  const { isMobile, isTablet } = useDevice();
  const location = useLocation();
  const isKittenPage = location.pathname.startsWith("/gato/kitten");

  return (
    <>
      {isMobile || isTablet ? (
        // Mobile/Tablet Layout
        <>
          {/* TITLE LANDING */}
          <div className="w-full flex justify-center px-10 mt-5">
            <picture>
              <source
                srcSet={`${beneficiosTitleImg["1x"]} 1x, ${beneficiosTitleImg["2x"]} 2x, ${beneficiosTitleImg["3x"]} 3x`}
              />
              <img
                src={beneficiosTitleImg["1x"]}
                alt={title}
                className="object-contain"
              />
            </picture>
          </div>

          {/* DESCRIPTION TEXT */}
          <div className="w-full flex justify-center px-10 mt-5">
            <div className="md:border-l-2 md:border-tertiary-red font-corbert">
              <p className="py-1 leading-snug text-sm lg:text-base text-center">
                {descriptionTexts[title]}
              </p>
            </div>
          </div>

          {/* PICTURE LANDING IMG */}
          <div className="mt-5 w-full flex items-center justify-center pb-3">
            <picture>
              <source
                srcSet={`${beneficiosImg["1x"]} 1x, ${beneficiosImg["2x"]} 2x, ${beneficiosImg["3x"]} 3x`}
              />
              <img
                src={beneficiosImg["1x"]}
                alt={title}
                className={clx(
                  "lg:max-w-2xl 2xl:max-w-5xl object-contain"
                  // isKittenPage && ""
                )}
              />
            </picture>
          </div>

          <Accordion title={title} color={color} />
        </>
      ) : (
        // Desktop Layout
        <>
          <div className="w-full flex flex-col lg:flex-row gap-5 lg:gap-10 xl:gap-20 items-center justify-center px-10 lg:pl-24 lg:pr-6 xl:pl-32 xl:pr-10 mt-2 lg:mt-5 xl:mt-10">
            {/* DESCRIPTION TEXT */}
            <div className="md:border-l-2 md:border-tertiary-red max-w-[40%] font-corbert">
              <p className="pl-3 py-1 leading-snug text-base">
                {descriptionTexts[title]}
              </p>
            </div>
            {/* TITLE LANDING */}
            <div className="xl:max-w-[40%]">
              <picture>
                <source
                  srcSet={`${beneficiosTitleImg["1x"]} 1x, ${beneficiosTitleImg["2x"]} 2x, ${beneficiosTitleImg["3x"]} 3x`}
                />
                <img
                  src={beneficiosTitleImg["1x"]}
                  alt={title}
                  className="object-contain"
                />
              </picture>
            </div>
          </div>
          {/* PICTURE LANDING IMG */}
          <div className="mt-2 lg:mt-5 xl:mt-10 w-full flex items-center justify-center">
            <picture>
              <source
                srcSet={`${beneficiosImg["1x"]} 1x, ${beneficiosImg["2x"]} 2x, ${beneficiosImg["3x"]} 3x`}
              />
              <img
                src={beneficiosImg["1x"]}
                alt={title}
                className="lg:max-w-2xl 2xl:max-w-5xl object-contain"
              />
            </picture>
          </div>
        </>
      )}
    </>
  );
};

export default Features;
