import React from "react";
import useDevice from "@/hooks/use-device";
import { clx } from "@/utils/clx";
import siegerLogo from "@/assets/icons/logo_sieger_perro_blanco.webp";
import siegerLogoCat from "@/assets/icons/logo_sieger_gato_blanco.webp";
import { useLocation } from "react-router-dom";

const HeroSection = ({ title, heroImg, headerText, color }) => {
  const { isMobile } = useDevice();
  const location = useLocation();
  const isCatPage = location.pathname.startsWith("/gato");

  return (
    <>
      <div className="w-full">
        <picture>
          <source
            srcSet={`${heroImg["1x"]} 1x, ${heroImg["2x"]} 2x, ${heroImg["3x"]} 3x`}
          />
          <img
            src={heroImg["1x"]}
            alt={title}
            className="lg:pt-3 w-full object-cover"
          />
        </picture>
      </div>
      {isMobile ? (
        <div
          className={clx(
            `flex flex-col items-center justify-center px-10 py-4`
          )}
          style={{ backgroundColor: color }}
        >
          <img
            src={isCatPage ? siegerLogoCat : siegerLogo}
            alt="Sieger Logo"
            className="max-w-20"
          />
          <h2 className="text-white text-center font-corbert text-sm">
            {headerText}
          </h2>
        </div>
      ) : null}
    </>
  );
};

export default HeroSection;
// {isMobile ?? (
//         <div
//           className={clx(
//             `flex flex-col items-center justify-center bg-[${color}] w-full min-h-70`
//           )}
//         >
//           <h2 className="text-white text-center font-corbert text-sm">
//             {headerText}
//           </h2>
//         </div>
//       )}
