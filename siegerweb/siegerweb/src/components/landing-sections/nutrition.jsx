import React from "react";

const Nutrition = ({ nutritionImg, title }) => {
  return (
    <>
      {nutritionImg && (
        <div className="mt-2 lg:mt-5 xl:mt-10 w-full flex items-center justify-center">
          <picture className="">
            <source
              srcSet={`${nutritionImg["1x"]} 1x, ${nutritionImg["2x"]} 2x, ${nutritionImg["3x"]} 3x`}
            />
            <img
              src={nutritionImg["1x"]}
              alt={title}
              className="object-contain"
            />
          </picture>
        </div>
      )}
    </>
  );
};

export default Nutrition;
