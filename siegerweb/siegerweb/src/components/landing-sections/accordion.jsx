import { ChevronDown } from "lucide-react";
import { useState } from "react";
import { clx } from "@/utils/clx";

// Data para diferentes tipos de acordeón
const accordionDataMap = {
  Puppy: [
    {
      title: "PROTECCIÓN NATURAL",
      content:
        "La L-Lisina, vitaminas y minerales específicos para esta etapa  promueven un óptimo desarrollo, favoreciendo las defensas naturales del cachorro.",
    },
    {
      title: "CONTRIBUYE AL APRENDIZAJE",
      content:
        "El proceso de aprendizaje se optimiza con la inclusión de <span class='font-corbertBold'>DHA</span>, un ácido graso decadena larga que nutre y mantiene saludable el cerebro y la retina.",
    },
    {
      title: "DESARROLLO MUSCULAR",
      content:
        "Las proteínas en porcentaje alto es clave para la optimización de la síntesis proteica y el desarrollo de músculos fuertes.",
    },
    {
      title: "DESARROLLO ÓSEO",
      content:
        "<span class='font-corbertBold'>El Calcio y Fósforo</span> garantiza que se desarrolle un sistema oseo saludable.",
    },
    {
      title: "SEGURIDAD DIGESTIVA",
      content:
        "Los probióticos promueven el desarrollo de una flora bacteriana saludable, fortaleciendo las defensas naturales del organismo del cachorro.",
    },
  ],
  DogAdult: [
    {
      title: "SALUD ORAL",
      content:
        "Los <span class='font-corbertBold'>perros de estructura pequeña</span> tiene más probabilidades de padecer halitosis. La inclusión de ayuda a mantener los dientes limpios y fuertes.",
    },
    {
      title: "SALUD CUTÁNEA",
      content:
        "Los <span class='font-corbertBold'>ácidos grasos Omega 3 y 6</span> refuerzan la integridad de la barrera cutánea, mejoran el brillo del pelaje y colaboran en los procesos inhibidores de la inflamación.",
    },
    {
      title: "MASA MUSCULAR MAGRA",
      content:
        "Un mayor aporte de <span class='font-corbertBold'>proteínas de alta digestibilidad</span> favorece la síntesis de nuevas miofibrillas, contribuyendo al desarrollo de una masa muscular magra y fuerte.",
    },
    {
      title: "SEGURIDAD DIGESTIVA",
      content:
        "Los <span class='font-corbertBold'>Probióticos</span> colaboran en el desarrollo de un microbioma saludable, fortaleciendo las defensas naturales del organismo.",
    },
    {
      title: "ARTICULACIONES Y HUESOS FUERTES",
      content:
        "Contiene <span class='font-corbertBold'>Glucosamina y Condoitrín Sulfato</span> para mantener las articulaciones sanas y una buena movilidad, especialmente en perros grandes.",
    },
  ],
  DogSenior: [
    {
      title: "SALUD COGNITIVA",
      content:
        "El aporte de <span class='font-corbertBold'>resveratrol</span> y un blend de antioxidantes contribuyen a mejorar la actividad cerebral.",
    },
    {
      title: "SALUD CUTÁNEA",
      content:
        "Los <span class='font-corbertBold'>ácidos grasos Omega 6 y 3</span> refuerzan la integridad de la barrera cutánea, favorecen el brillo del pelaje y colaboran en los procesos antiinflamatorios.",
    },
    {
      title: "TONICIDAD MUSCULAR",
      content:
        "Las <span class='font-corbertBold'>proteínas de alta digestibilidad</span> optimizan la síntesis proteíca, promoviendo una masa muscular fuerte y magra.",
    },
    {
      title: "ARTICULACIONES Y HUESOS FUERTES",
      content:
        "La <span class='font-corbertBold'>Glucosamina y Condroitín Sulfato</span> colaboran en fortalecer las articulaciones y huesos.",
    },
    {
      title: "SEGURIDAD DIGESTIVA",
      content:
        "Los <span class='font-corbertBold'>Probióticos</span> colaboran en el desarrollo de un microbioma saludable, fortaleciendo las defensas naturales del organismo.",
    },
  ],
  Kitten: [
    {
      title: "APOYA AL DESARROLLO ÓSEO Y DENTAL",
      content:
        "La incorporación equilibrada de <span class='font-corbertBold'>calcio y fósforo, vitamina D</span> y minerales, acompaña el cambio de dentición, y promueve el crecimiento de huesos fuertes.",
    },
    {
      title: "MEJORA EL PROCESO DE APRENDIZAJE Y DESARROLLA LA VISIÓN",
      content:
        "El <span class='font-corbertBold'>aporte de DHA</span> favorece el funcionamiento neuronal y ayuda en el proceso de formación de la retina. ",
    },
    {
      title: "FORTALECE EL SISTEMA INMUNOLÓGICO",
      content:
        "La <span class='font-corbertBold'>incorporación de L-Lisina</span>, participa de la síntesis de anticuerpos y enzimas que mejoran el sistema inmune. Su utilización es recomendable ante situaciones de stress o en enfermedades respiratorias como la rinotraqueitis felina. Las <span class='font-corbertBold'>vitaminas y antioxidantes</span> fortalecen las defensas naturales.",
    },
    {
      title: "PROMUEVE EL CRECIMIENTO SALUDABLE",
      content:
        "Un <span class='font-corbertBold'>elevado contenido energético</span> sostiene las necesidades nutricionales durante el crecimiento.",
    },
  ],
  CatAdult: [
    {
      title: "CONTROL DE BOLAS DE PELO",
      content:
        "La incorporación de <span class='font-corbertBold'>fibras naturales</span> favorecen la eliminación del pelo ingerido durante el acicalamiento y mejoran la consistencia y aroma de la heces.  ",
    },
    {
      title: "PELAJE SALUDABLE",
      content:
        "Los <span class='font-corbertBold'>ácidos grasos omega 3 y 6</span> refuerzan la integridad de la barrera cutánea, favorecen el brillo del pelaje y colaboran en los procesos antiinflamatorios.",
    },
    {
      title: "PROTECCIÓN ARTICULAR",
      content:
        "La <span class='font-corbertBold'>glucosamina y condoitrín sulfato</span>, promueven el buen funcionamiento artícular: hidratan y nutren el cartílago, favorenciendo la movilidad del gato adulto.",
    },
    {
      title: "PROTECCIÓN DEL TRACTO URINARIO",
      content:
        "Ayuda a proteger el tracto urinario gracias a su <span class='font-corbertBold'>reducido nivel de magensio</span>. Mantiene un rango de pH fisiológico en orina reduciendo la probabilidad de formación de cristales.",
    },
  ],
  CatSenior: [
    {
      title: "VITALIDAD Y LONGEVIDAD",
      content:
        "Convina <span class='font-corbertBold'>resveratrol, aceite de coco, té verde y antioxidantes naturales</span>, que contribuyen a la protección de las células y los tejidos ayudando a retrasar signos del envejecimiento.",
    },
    {
      title: "SALUD DENTAL",
      content:
        "Las Croquetas están específicamente diseñadas con textura, forma y tamaño con el objetivo de promover la masticación y fomentar una fricción mecánica que ayuda a eliminar la placa bacteriana. La incorporación de <span class='font-corbertBold'>hexametafosfato</span> de sodio retiene el calcio de la saliva, y disminuye la formación de sarro protegiendo la salud dental.",
    },
    {
      title: "PROTECCIÓN ARTICULAR",
      content:
        "La <span class='font-corbertBold'>glucosamina y condoitrín sulfato</span>, promueven el buen funcionamiento articular y colaboran en la hidratación y nutrición del cartílago, favoreciendo la movilidad del gato maduro.",
    },
    {
      title: "SALUD RENAL",
      content:
        "Su fórmula reducida en sodio, con un adecuado equilibrio de minerales y una exclusiva combinación sinérgica de arginina, <span class='font-corbertBold'>betacaroteno y Vitamina C</span>, promueven la salud renal del gato maduro. ",
    },
  ],
};

const AccordionItem = ({ title, content, color, isOpen, onToggle, index }) => {
  return (
    <div className="border-b border-gray-200 last:border-b-0 ">
      {/* Header */}
      <button
        onClick={() => onToggle(index)}
        className={clx(
          `w-full px-6 py-4 text-left font-semibold text-white transition-all duration-300 hover:opacity-90 focus:outline-none first-of-type:border-t`,
          isOpen ? `text-white` : "bg-white text-secondary-gray"
        )}
        style={{ backgroundColor: isOpen ? color : undefined }}
      >
        <div className="flex items-center justify-between">
          <span className="text-base font-gothamMedium">{title}</span>
          <ChevronDown
            className={`w-5 h-5 transition-transform duration-300 ${
              isOpen ? "rotate-180" : "rotate-0"
            }`}
          />
        </div>
      </button>

      {/* Content */}
      <div
        className={`overflow-hidden transition-all duration-300 ease-in-out ${
          isOpen ? "max-h-96 opacity-100" : "max-h-0 opacity-0"
        }`}
      >
        <div
          className="px-6 py-4 bg-white text-gray-700 leading-relaxed font-corbert text-sm"
          dangerouslySetInnerHTML={{ __html: content }}
        ></div>
      </div>
    </div>
  );
};

const Accordion = ({ title, color }) => {
  const [openIndex, setOpenIndex] = useState(0); // Primer elemento abierto por defecto

  const handleToggle = (index) => {
    setOpenIndex(openIndex === index ? -1 : index);
  };

  // Obtener la data basada en el tipo
  const accordionData = accordionDataMap[title] || accordionDataMap.Puppy;

  return (
    <div className="max-w-md mx-auto bg-white shadow-lg overflow-hidden">
      {accordionData.map((item, index) => (
        <AccordionItem
          key={index}
          index={index}
          title={item.title}
          content={item.content}
          color={color}
          isOpen={openIndex === index}
          onToggle={handleToggle}
        />
      ))}
    </div>
  );
};
export default Accordion;
