import React from "react";
import { clx } from "../../utils/clx";

const FeaturedProducts = ({ title }) => {
  const colorClasses = {
    puppy: "text-neutrals-puppy",
    dogadult: "text-neutrals-dogAdult",
    dogsenior: "text-neutrals-dogSenior",
    kitten: "text-neutrals-kitten",
    catadult: "text-neutrals-catAdult",
    catsenior: "text-neutrals-catSenior",
  };

  let displayName = title;
  switch (title.toLowerCase()) {
    case "dogadult":
      displayName = "Adult";
      break;
    case "dogsenior":
      displayName = "Senior";
      break;
    case "catadult":
      displayName = "Adult";
      break;
    case "catsenior":
      displayName = "Senior";
      break;
    default:
      displayName = title;
  }

  return (
    <>
      <h3 className="text-2xl lg:text-2xl xl:text-3xl font-gothamMedium px-10 lg:pl-24 lg:pr-6 xl:pl-32 mt-10 text-center lg:text-left">
        Conocé nuestra <br className="block md:hidden" />
        <span className={clx(colorClasses[title.toLowerCase()])}>
          Línea {displayName}
        </span>
      </h3>
      <div>
        {/* todo: agregar alimentos del back filtrados por etapa */}
        alimentos del back filtrados por etapa
      </div>
    </>
  );
};

export default FeaturedProducts;
