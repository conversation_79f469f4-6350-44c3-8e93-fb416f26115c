import { clx } from "../utils/clx";

const CustomCheckbox = ({
  checked = false,
  onCheckedChange,
  label,
  classLabel,
  //   className,
}) => {
  const handleChange = () => {
    onCheckedChange?.(!checked);
  };

  return (
    <div className="checkbox-wrapper">
      <label className="custom-checkbox-label cursor-pointer">
        <input
          type="checkbox"
          checked={checked}
          onChange={handleChange}
          className="custom-checkbox-input"
        />
        <span
          className={clx("custom-checkbox-box", checked && "checked")}
        ></span>
        {label && (
          <span className={clx("ml-2 text-sm", classLabel)}>{label}</span>
        )}
      </label>
    </div>
  );
};

export default CustomCheckbox;
