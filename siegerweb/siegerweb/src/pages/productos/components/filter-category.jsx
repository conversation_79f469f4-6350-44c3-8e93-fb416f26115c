import { X } from "lucide-react";
import CustomCheckbox from "@/components/custom-checkbox";

export function FilterCategory({
  category,
  selectedValues,
  onFilterChange,
  onClearCategory,
}) {
  const hasActiveFilters = selectedValues.length > 0;

  return (
    <div className="space-y-3 border-b pb-4 border-gray-300">
      <div className="flex items-center justify-between">
        <h4 className="font-gothamMedium text-sm text-foreground">
          {category.title}
        </h4>
        {hasActiveFilters && (
          <button
            onClick={onClearCategory}
            className="h-auto w-fit p-1 text-xs text-muted-foreground hover:text-foreground"
          >
            <X className="h-3 w-3" />
            <span className="sr-only">Limpiar {category.title}</span>
          </button>
        )}
      </div>

      <ul className="space-y-2">
        {category.options.map((option) => {
          const isChecked = selectedValues.includes(option.value);

          return (
            <li key={option.id}>
              <label className="flex items-center cursor-pointer group w-fit">
                <CustomCheckbox
                  checked={isChecked}
                  onCheckedChange={(checked) =>
                    onFilterChange(option.value, checked === true)
                  }
                />
                <span className="text-sm pb-0.5 font-corbert">
                  {option.label}
                </span>
              </label>
            </li>
          );
        })}
      </ul>
    </div>
  );
}
