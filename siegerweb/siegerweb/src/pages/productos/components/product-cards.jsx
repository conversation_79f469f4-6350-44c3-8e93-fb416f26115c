import { useFilteredArticles } from "@/hooks/queries/use-data";
import { Link } from "react-router-dom";
import { LoaderCircle, ChevronLeft, ChevronRight } from "lucide-react";
import { useEffect } from "react";
import useDevice from "@/hooks/use-device";

const ProductCards = ({
  filters = {},
  pagination = {},
  onPaginationChange,
}) => {
  const { data, isLoading, error } = useFilteredArticles(filters, pagination);
  const products = data?.items || [];
  const paginationInfo = data?.pagination || {};
  const { isMobile, isTablet } = useDevice();

  useEffect(() => {
    window.scrollTo({ top: 0, behavior: "smooth" });
  }, [pagination.page]);

  const handlePageChange = (newPage) => {
    if (onPaginationChange && newPage >= 1) {
      onPaginationChange({ page: newPage });
    }
  };

  if (isLoading) {
    return (
      <div className="flex h-screen w-full items-center justify-center">
        <LoaderCircle size={40} color="#8e8e8e" className="animate-spin" />
      </div>
    );
  }

  if (error || products.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-600">
          No se encontraron productos con los filtros seleccionados.
        </p>
      </div>
    );
  }

  return (
    <>
      <div
        className={`grid gap-4 ${
          isMobile
            ? "grid-cols-2"
            : isTablet
            ? "grid-cols-3"
            : "grid-cols-4 lg:grid-cols-5"
        }`}
      >
        {products.map((product, key) => (
          <Link
            key={key}
            to={`/${product.id}/${product.especie.toLowerCase()}/${product.name
              ?.toLowerCase()
              ?.trim()
              .replace(/(\+)\s*/g, "$1")
              .replace(/\s+/g, "-")}`}
            className="group"
          >
            <div className="flex flex-col justify-end gap-2 text-xs flex-grow px-4 pt-3 pb-2 border border-gray-300 rounded-lg min-h-[220px]">
              <img
                src={product.url_frente}
                alt={product.name}
                className="w-full h-full group-hover:scale-110 transition-transform duration-200"
              />
              <h3 className="text-center font-gothamMedium text-xs place-self-end">
                {product.name}
              </h3>
            </div>
          </Link>
        ))}
      </div>

      {/* Pagination Controls */}
      {products.length > 0 && (
        <div className="mt-8 flex flex-col sm:flex-row items-center justify-between gap-4">
          {paginationInfo.total && (
            <span className="text-xs text-gray-600 font-corbert">
              Mostrando {(paginationInfo.page - 1) * paginationInfo.limit + 1} -{" "}
              {Math.min(
                paginationInfo.page * paginationInfo.limit,
                paginationInfo.total
              )}{" "}
              de {paginationInfo.total} productos
            </span>
          )}
          <div className="lg:ml-28 flex gap-2">
            <button
              onClick={() =>
                handlePageChange((paginationInfo.page || pagination.page) - 1)
              }
              disabled={(paginationInfo.page || pagination.page) <= 1}
              className="flex items-center gap-1 px-3 py-2 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ChevronLeft size={16} />
              Anterior
            </button>

            <div className="flex items-center gap-1">
              {paginationInfo.pages && paginationInfo.pages > 1 && (
                <>
                  {Array.from(
                    { length: Math.min(5, paginationInfo.pages) },
                    (_, i) => {
                      const currentPage =
                        paginationInfo.page || pagination.page;
                      const startPage = Math.max(1, currentPage - 2);
                      const pageNum = startPage + i;
                      if (pageNum > paginationInfo.pages) return null;

                      return (
                        <button
                          key={pageNum}
                          onClick={() => handlePageChange(pageNum)}
                          className={`px-3 py-2 text-sm border rounded ${
                            pageNum === currentPage
                              ? "bg-secondary-red text-white border-secondary-red"
                              : "border-gray-300 hover:bg-gray-50"
                          }`}
                        >
                          {pageNum}
                        </button>
                      );
                    }
                  )}
                </>
              )}
            </div>

            <button
              onClick={() =>
                handlePageChange((paginationInfo.page || pagination.page) + 1)
              }
              disabled={
                (paginationInfo.page || pagination.page) >=
                (paginationInfo.pages || 999)
              }
              className="flex items-center gap-1 px-3 py-2 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Siguiente
              <ChevronRight size={16} />
            </button>
          </div>
        </div>
      )}
    </>
  );
};

export default ProductCards;
