import { useEffect } from "react";
import { X } from "lucide-react";

import { useFilters } from "@/hooks/use-filters";
import { FILTER_CATEGORIES } from "@/data/filter-config";
import { FilterCategory } from "./filter-category";

const ProductFilters = ({ onFiltersChange, className, onClose, isMobile }) => {
  const {
    filters,
    updateFilter,
    clearCategory,
    clearAllFilters,
    getActiveFiltersCount,
    getFilterQuery,
  } = useFilters();

  const activeFiltersCount = getActiveFiltersCount();

  useEffect(() => {
    if (onFiltersChange) {
      const query = getFilterQuery();
      onFiltersChange(query);
    }
  }, [filters]);

  const handleApplyFilters = () => {
    if (onClose) {
      onClose();
    }
  };

  if (isMobile) {
    return (
      <div className="h-full flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b">
          <h3 className="font-gothamMedium text-lg">Filtros</h3>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-full"
          >
            <X size={20} />
          </button>
        </div>

        {/* Filters Content */}
        <div className="flex-1 overflow-y-auto p-4 space-y-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {activeFiltersCount > 0 && (
                <div className="font-gotham text-xs px-1.5 py-1 rounded-md bg-gray-300 text-neutral-700">
                  {activeFiltersCount}
                </div>
              )}
            </div>

            {activeFiltersCount > 0 && (
              <button
                onClick={clearAllFilters}
                className="font-corbert text-xs hover:underline text-red-600"
              >
                Limpiar todo
              </button>
            )}
          </div>

          <div className="space-y-6">
            {FILTER_CATEGORIES.map((category) => (
              <FilterCategory
                key={category.id}
                category={category}
                selectedValues={filters[category.id] || []}
                onFilterChange={(optionValue, checked) =>
                  updateFilter(category.id, optionValue, checked)
                }
                onClearCategory={() => clearCategory(category.id)}
              />
            ))}
          </div>
        </div>

        {/* Footer */}
        <div className="p-4 border-t bg-white">
          <button
            onClick={handleApplyFilters}
            className="w-full bg-secondary-red text-white py-3 rounded-lg font-gothamMedium"
          >
            Aplicar filtros
          </button>
        </div>
      </div>
    );
  }

  // Desktop version
  return (
    <aside className={`w-1/5 sticky top-0 z-40 p-6 space-y-6 ${className}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <h3 className="font-gothamMedium text-base">Filtros</h3>
          {activeFiltersCount > 0 && (
            <div className="font-gotham text-xs px-1.5 py-1 rounded-md bg-gray-300 text-neutral-700">
              {activeFiltersCount}
            </div>
          )}
        </div>

        {activeFiltersCount > 0 && (
          <button
            onClick={clearAllFilters}
            className="font-corbert text-xs hover:underline"
          >
            Limpiar todo
          </button>
        )}
      </div>

      <div className="space-y-6">
        {FILTER_CATEGORIES.map((category) => (
          <FilterCategory
            key={category.id}
            category={category}
            selectedValues={filters[category.id] || []}
            onFilterChange={(optionValue, checked) =>
              updateFilter(category.id, optionValue, checked)
            }
            onClearCategory={() => clearCategory(category.id)}
          />
        ))}
      </div>
    </aside>
  );
};
export default ProductFilters;
