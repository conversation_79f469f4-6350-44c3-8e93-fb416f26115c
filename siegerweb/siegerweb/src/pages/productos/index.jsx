import HeaderProductos from "./components/header";
import ProductFilters from "./components/product-filters";
import ProductCards from "./components/product-cards";
import { useEffect, useState } from "react";
import useDevice from "@/hooks/use-device";
import { Filter } from "lucide-react";

const Productos = () => {
  const [filters, setFilters] = useState({});
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    sort_by: "name",
    sort_dir: "asc",
  });
  const [showFilters, setShowFilters] = useState(false);
  const { isMobile, isTablet, isDesktop } = useDevice();

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const handleFiltersChange = (query) => {
    const backendFilters = {};

    if (query.especie !== undefined) {
      backendFilters.especie = query.especie;
    }

    if (query.etapas?.length) {
      backendFilters.etapas = query.etapas;
    }

    if (query.tipo !== undefined) {
      backendFilters.tipo = query.tipo;
    }

    if (query.beneficios?.length) {
      backendFilters.beneficios = query.beneficios;
    }

    setFilters(backendFilters);
    setPagination((prev) => ({ ...prev, page: 1 }));
  };

  const handlePaginationChange = (newPagination) => {
    setPagination((prev) => {
      const newState = { ...prev, ...newPagination };
      return newState;
    });
  };

  return (
    <>
      <HeaderProductos />

      {/* Mobile/Tablet Filter Button */}
      {(isMobile || isTablet) && (
        <div className="px-5 mb-4">
          <button
            onClick={() => setShowFilters(true)}
            className="flex items-center gap-2 px-4 py-2 bg-white border border-gray-300 rounded-lg shadow-sm"
          >
            <Filter size={16} />
            <span className="font-gothamMedium text-sm">Filtros</span>
          </button>
        </div>
      )}

      <div className="flex gap-5 px-5 lg:px-10 w-full">
        {/* Desktop Filters */}
        {isDesktop && <ProductFilters onFiltersChange={handleFiltersChange} />}

        {/* Mobile/Tablet Filter Modal */}
        {(isMobile || isTablet) && showFilters && (
          <div className="fixed inset-0 z-50 bg-black bg-opacity-50">
            <div className="fixed inset-y-0 left-0 w-full max-w-sm bg-white shadow-xl">
              <ProductFilters
                onFiltersChange={handleFiltersChange}
                onClose={() => setShowFilters(false)}
                isMobile={true}
              />
            </div>
          </div>
        )}

        <main
          className={`flex-1 px-3 lg:px-5 pt-2 ${isDesktop ? "" : "w-full"}`}
        >
          <ProductCards
            filters={filters}
            pagination={pagination}
            onPaginationChange={handlePaginationChange}
          />
        </main>
      </div>
    </>
  );
};
export default Productos;
