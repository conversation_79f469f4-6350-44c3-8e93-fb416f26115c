import LandingContent from "../../components/landing-sections/landing-content";
import useDevice from "@/hooks/use-device";
import headerMb1x from "@/assets/images/landings/gato/senior/header-mb.webp";
import headerMb2x from "@/assets/images/landings/gato/senior/<EMAIL>";
import headerMb3x from "@/assets/images/landings/gato/senior/<EMAIL>";
import header1x from "@/assets/images/landings/gato/senior/header.webp";
import header2x from "@/assets/images/landings/gato/senior/<EMAIL>";
import header3x from "@/assets/images/landings/gato/senior/<EMAIL>";
import beneficiosTitleMb1x from "@/assets/images/landings/gato/senior/beneficios-title-mb.webp";
import beneficiosTitleMb2x from "@/assets/images/landings/gato/senior/<EMAIL>";
import beneficiosTitleMb3x from "@/assets/images/landings/gato/senior/<EMAIL>";
import beneficiosTitle1x from "@/assets/images/landings/gato/senior/beneficios-title.webp";
import beneficiosTitle2x from "@/assets/images/landings/gato/senior/<EMAIL>";
import beneficiosTitle3x from "@/assets/images/landings/gato/senior/<EMAIL>";
import beneficiosMb1x from "@/assets/images/landings/gato/senior/beneficios-mb.webp";
import beneficiosMb2x from "@/assets/images/landings/gato/senior/<EMAIL>";
import beneficiosMb3x from "@/assets/images/landings/gato/senior/<EMAIL>";
import beneficios1x from "@/assets/images/landings/gato/senior/beneficios.webp";
import beneficios2x from "@/assets/images/landings/gato/senior/<EMAIL>";
import beneficios3x from "@/assets/images/landings/gato/senior/<EMAIL>";

const CatSenior = () => {
  const { isMobile } = useDevice();

  return (
    <div>
      <LandingContent
        title="CatSenior"
        color="#9B0955"
        headerText="Sieger Katze Senior aporta en cada bocado ciencia, salud y placer para que los gatos de +7 años y sus familias puedan disfrutar plenamente de esta etapa de la vida."
        heroImg={
          isMobile
            ? {
                "1x": headerMb1x,
                "2x": headerMb2x,
                "3x": headerMb3x,
              }
            : {
                "1x": header1x,
                "2x": header2x,
                "3x": header3x,
              }
        }
        beneficiosTitleImg={
          isMobile
            ? {
                "1x": beneficiosTitleMb1x,
                "2x": beneficiosTitleMb2x,
                "3x": beneficiosTitleMb3x,
              }
            : {
                "1x": beneficiosTitle1x,
                "2x": beneficiosTitle2x,
                "3x": beneficiosTitle3x,
              }
        }
        beneficiosImg={
          isMobile
            ? {
                "1x": beneficiosMb1x,
                "2x": beneficiosMb2x,
                "3x": beneficiosMb3x,
              }
            : {
                "1x": beneficios1x,
                "2x": beneficios2x,
                "3x": beneficios3x,
              }
        }
      />
    </div>
  );
};
export default CatSenior;
