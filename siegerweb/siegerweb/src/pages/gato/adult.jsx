import LandingContent from "../../components/landing-sections/landing-content";
import useDevice from "@/hooks/use-device";
import headerMb1x from "@/assets/images/landings/gato/adult/header-mb.webp";
import headerMb2x from "@/assets/images/landings/gato/adult/<EMAIL>";
import headerMb3x from "@/assets/images/landings/gato/adult/<EMAIL>";
import header1x from "@/assets/images/landings/gato/adult/header.webp";
import header2x from "@/assets/images/landings/gato/adult/<EMAIL>";
import header3x from "@/assets/images/landings/gato/adult/<EMAIL>";
import beneficiosTitleMb1x from "@/assets/images/landings/gato/adult/beneficios-title-mb.webp";
import beneficiosTitleMb2x from "@/assets/images/landings/gato/adult/<EMAIL>";
import beneficiosTitleMb3x from "@/assets/images/landings/gato/adult/<EMAIL>";
import beneficiosTitle1x from "@/assets/images/landings/gato/adult/beneficios-title.webp";
import beneficiosTitle2x from "@/assets/images/landings/gato/adult/<EMAIL>";
import beneficiosTitle3x from "@/assets/images/landings/gato/adult/<EMAIL>";
import beneficiosMb1x from "@/assets/images/landings/gato/adult/beneficios-mb.webp";
import beneficiosMb2x from "@/assets/images/landings/gato/adult/<EMAIL>";
import beneficiosMb3x from "@/assets/images/landings/gato/adult/<EMAIL>";
import beneficios1x from "@/assets/images/landings/gato/adult/beneficios.webp";
import beneficios2x from "@/assets/images/landings/gato/adult/<EMAIL>";
import beneficios3x from "@/assets/images/landings/gato/adult/<EMAIL>";
import nutricionMb1x from "@/assets/images/landings/gato/adult/nutricion-mb.webp";
import nutricionMb2x from "@/assets/images/landings/gato/adult/<EMAIL>";
import nutricionMb3x from "@/assets/images/landings/gato/adult/<EMAIL>";
import nutricion1x from "@/assets/images/landings/gato/adult/nutricion.webp";
import nutricion2x from "@/assets/images/landings/gato/adult/<EMAIL>";
import nutricion3x from "@/assets/images/landings/gato/adult/<EMAIL>";

const CatAdult = () => {
  const { isMobile } = useDevice();

  return (
    <div>
      <LandingContent
        title="CatAdult"
        color="#0047BB"
        headerText="Sieger Katze Adult High Quality Protein es un alimento indicado a partir del primer año de vida, para gatos que inician su etapa de adultez. Esta fórmula brinda beneficios claves para que el gato disfrute de una vida plena y activa, preparándolo para transitar con vitalidad la siguiente fase de vida.."
        heroImg={
          isMobile
            ? {
                "1x": headerMb1x,
                "2x": headerMb2x,
                "3x": headerMb3x,
              }
            : {
                "1x": header1x,
                "2x": header2x,
                "3x": header3x,
              }
        }
        beneficiosTitleImg={
          isMobile
            ? {
                "1x": beneficiosTitleMb1x,
                "2x": beneficiosTitleMb2x,
                "3x": beneficiosTitleMb3x,
              }
            : {
                "1x": beneficiosTitle1x,
                "2x": beneficiosTitle2x,
                "3x": beneficiosTitle3x,
              }
        }
        beneficiosImg={
          isMobile
            ? {
                "1x": beneficiosMb1x,
                "2x": beneficiosMb2x,
                "3x": beneficiosMb3x,
              }
            : {
                "1x": beneficios1x,
                "2x": beneficios2x,
                "3x": beneficios3x,
              }
        }
        nutritionImg={
          isMobile
            ? {
                "1x": nutricionMb1x,
                "2x": nutricionMb2x,
                "3x": nutricionMb3x,
              }
            : {
                "1x": nutricion1x,
                "2x": nutricion2x,
                "3x": nutricion3x,
              }
        }
      />
    </div>
  );
};
export default CatAdult;
