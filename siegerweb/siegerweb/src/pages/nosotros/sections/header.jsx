import React from "react";
import Fabric1x from "@/assets/images/aboutUs/fabric.webp";
import Fabric2x from "@/assets/images/aboutUs/<EMAIL>";
import Fabric3x from "@/assets/images/aboutUs/<EMAIL>";

const Header = () => {
  return (
    <div className="flex flex-col gap-5 lg:gap-10 lg:flex-row w-full items-center justify-center px-5 lg:px-10">
      <div className="mt-5">
        <h2 className="text-xl md:text-2xl lg:text-4xl font-gothamMedium max-w-sm text-center lg:text-left">
          <span className="text-secondary-red"><PERSON>r,</span> pasión por la
          nutrición. Amor por las mascotas.
        </h2>
        <p className="text-xs font-corbert max-w-sm mt-1 text-center lg:text-left">
          En Sieger transformamos la nutrición de perros y gatos con
          investigación, calidad y mucho amor. Más de 15 años de experiencia, un
          equipo de veterinarios expertos y la más alta tecnología nos impulsan
          a crear alimentos que cuidan y fortalecen ese vínculo único que
          compartís con tu mascota.
        </p>
      </div>
      <div className="">
        <picture className="">
          <source
            media="(min-width: 300px)"
            srcSet={`${Fabric1x} 1x, ${Fabric2x} 2x, ${Fabric3x} 3x`}
          />
          <img
            src={Fabric1x}
            alt="Fábrica Sieger"
            className="max-w-[700px] md:max-w-[500px] xl:max-w-[700px] object-contain"
          />
        </picture>
      </div>
    </div>
  );
};

export default Header;
