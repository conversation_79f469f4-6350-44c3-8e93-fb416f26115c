import React from "react";
import IconSquare from "@/assets/icons/icon-square.webp";
import IconPencil from "@/assets/icons/icon-pencil.webp";
import IconMail from "@/assets/icons/icon-mail.webp";
import AlicanCompany1x from "@/assets/images/aboutUs/alican.webp";
import AlicanCompany2x from "@/assets/images/aboutUs/<EMAIL>";
import AlicanCompany3x from "@/assets/images/aboutUs/<EMAIL>";
import WorkingMan1x from "@/assets/images/aboutUs/work.webp";
import WorkingMan2x from "@/assets/images/aboutUs/<EMAIL>";
import WorkingMan3x from "@/assets/images/aboutUs/<EMAIL>";

const Main = () => {
  return (
    <div className="w-full flex flex-col items-center justify-center px-5 mt-10 lg:mt-24 lg:pb-16">
      <h4 className="font-gothamMedium text-lg md:text-xl lg:text-2xl text-left md:text-center lg:text-left">
        INVESTIGACIÓN Y DESARROLLO
      </h4>
      <h5 className="text-sm font-gothamMedium text-left md:text-center lg:text-left">
        Ciencia y pasión al servicio de tu mascota.
      </h5>
      <p className="max-w-xl lg:max-w-3xl text-xs font-corbert mt-2 text-left md:text-center lg:text-left lg:pl-0">
        Sieger es una marca de alimento balanceado que evolucionó, al igual que
        el vínculo entre los humanos y los perros y gatos: hoy entendemos que su
        alimentación es tan importante como la nuestra, ya que sabemos que una
        óptima nutrición es sinónimo de óptima calidad y esperanza de vida.
      </p>
      <br />
      <p className="max-w-xl lg:max-w-3xl text-xs font-corbert mt-2 text-left md:text-center lg:text-left lg:pl-0">
        Sieger es una marca con más de 15 años de trayectoria en el mercado de
        petfood. Desarrollada y formulada por un equipo de veterinarios, es
        producida y distribuida por la empresa Alican, cuya planta de producción
        de secos se encuentra en Alcira Gigena y la de húmedos en Juarez Celmán,
        ambas en la provincia de Córdoba,.Argentina.
      </p>

      <div className="flex flex-col lg:flex-row items-center justify-start mx-1 lg:mr-0 lg:ml-5 gap-16 mt-10">
        <div className="flex flex-col gap-2">
          <div className="flex gap-3 items-center ">
            <img
              src={IconMail}
              alt="Icono correo"
              className="object-contain max-w-10 aspect-square"
            />
            <p className="text-sm font-gothamMedium">Nutrición Avanzada</p>
          </div>

          <div className="flex gap-3 items-center mt-3">
            <img
              src={IconPencil}
              alt="Icono lápiz"
              className="object-contain max-w-10 aspect-square"
            />
            <p className="text-sm font-gothamMedium">
              Equipo Veterinario Especializado
            </p>
          </div>

          <div className="flex gap-3 items-center mt-3">
            <img
              src={IconSquare}
              alt="Icono producción"
              className="object-contain max-w-10 aspect-square"
            />
            <p className="text-sm font-gothamMedium">
              Producción de Excelencia
            </p>
          </div>
        </div>
        <picture className="">
          <source
            media="(min-width: 300px)"
            srcSet={`${AlicanCompany1x} 1x, ${AlicanCompany2x} 2x, ${AlicanCompany3x} 3x`}
          />
          <img
            src={AlicanCompany1x}
            alt="Alican Central"
            className="max-w-[420px] object-contain"
          />
        </picture>
      </div>

      <div className="flex flex-col lg:flex-row items-center justify-start lg:ml-5 gap-10 lg:gap-16 mt-10">
        <picture className="">
          <source
            media="(min-width: 300px)"
            srcSet={`${WorkingMan1x} 1x, ${WorkingMan2x} 2x, ${WorkingMan3x} 3x`}
          />
          <img
            src={WorkingMan1x}
            alt="Hombre trabajando en fábrica"
            className="max-w-[350px] object-contain"
          />
        </picture>

        <div className="lg:mt-12">
          <h5 className="text-2xl font-gothamMedium">
            EL MÁS ELEVADO <br /> ESTÁNDAR DE CALIDAD
          </h5>
          <p className="text-xs font-gothamMedium">
            Calidad certificada para una vida mejor.
          </p>
          <div className="max-w-[295px]">
            <p className="mt-5 text-xs font-corbert">
              La rigurosidad en la selección de las materias primas, los
              controles minuciosos a lo largo de todo el proceso, junto a la
              tecnología de punta, aseguran productos de altísima calidad
              certificada, atributo fundamental para una gama de productos
              comercializados exclusivamente en canal veterinario.
            </p>
            <p className="mt-5 text-xs font-corbert">
              En Sieger creemos que el vínculo con nuestros perros y gatos es
              poderoso, que nos da vitalidad, paz y alegría, como ninguna otra
              relación te lo da. Es un vínculo que no tiene nombre, pero que no
              hace falta que lo tenga, porque si tenés la fortuna de tener un
              perro o gato ya entendés de qué estamos hablando.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Main;
