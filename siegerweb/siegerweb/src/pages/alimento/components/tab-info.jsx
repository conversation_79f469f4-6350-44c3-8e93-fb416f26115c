import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import ContNutricional from "./cont-nutricional";
import GuiaAlimentacion from "./guia-alimentacion";
import { clx } from "../../../utils/clx";

const TabInfo = ({ articulo }) => {
  const tabs = [
    { id: "descripcion", label: "Descripción" },
    { id: "guia", label: "Guía de alimentación" },
    { id: "nutricional", label: "Información nutricional" },
  ];

  const [activeTab, setActiveTab] = useState(tabs[0]?.id);
  const [indicatorStyle, setIndicatorStyle] = useState({});
  const tabRefs = useRef({});
  const [accordionState, setAccordionState] = useState({ descripcion: true });

  const tabContents = useMemo(
    () => ({
      descripcion: (
        <div className="my-2 px-2 py-1 font-corbert text-sm font-light text-neutrals-blackie md:px-0 md:text-base">
          <p>{articulo.descripcion}</p>
        </div>
      ),
      guia: <GuiaAlimentacion />,
      nutricional: (
        <div className=" font-corbert text-base font-light text-neutrals-blackie">
          <ContNutricional articulo={articulo} />
        </div>
      ),
    }),
    [articulo]
  );

  const updateIndicatorPosition = useCallback((tabId) => {
    const currentTab = tabRefs.current[tabId];
    if (currentTab) {
      setIndicatorStyle({
        left: currentTab.offsetLeft,
        width: currentTab.offsetWidth,
        backgroundColor: "#FB333E",
      });
    }
  }, []);

  useEffect(() => {
    updateIndicatorPosition(activeTab);
  }, [activeTab, updateIndicatorPosition]);

  const toggleAccordion = useCallback((tabId) => {
    setAccordionState((prevState) => ({
      ...prevState,
      [tabId]: !prevState[tabId],
    }));
  }, []);

  const handleTabClick = useCallback((tabId) => {
    setActiveTab(tabId);
  }, []);

  return (
    <div className="relative z-0 mx-auto w-full max-w-4xl px-4 py-4 md:py-12 lg:py-0">
      {/* Tabs en desktop */}
      <div className="relative z-0 mt-10 hidden flex-col items-end justify-around md:flex md:flex-row">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => handleTabClick(tab.id)}
            ref={(el) => (tabRefs.current[tab.id] = el)}
            className={`relative z-0 px-3 py-1 w-full border-b-2 border-primary-red/10 font-gothamMedium text-lg transition-colors duration-200 ${
              activeTab === tab.id
                ? "text-neutrals-blackie"
                : "text-neutrals-blackie/60 hover:text-neutrals-blackie/80"
            }`}
          >
            {tab.label}
          </button>
        ))}

        {/* indicador pestañas */}
        <div
          className="absolute bottom-0 h-0.5 transition-all duration-300 ease-in-out"
          style={indicatorStyle}
        />
      </div>

      {/* Contenido del tab activo en desktop */}
      <div className="hidden md:mt-4 md:block md:pb-14 font-corbert">
        {tabContents[activeTab]}
      </div>

      {/* Acordeón en mobile */}
      <div className="block border-neutrals-blackie md:hidden">
        {tabs.map((tab) => {
          const isOpen = accordionState[tab.id];

          return (
            <div
              key={tab.id}
              className={clx(
                "mb-1.5 overflow-hidden border border-neutrals-blackie",
                "rounded-xl"
              )}
            >
              <button
                onClick={() => toggleAccordion(tab.id)}
                className={clx(
                  "w-full px-4 py-3 text-center font-gotham text-lg font-semibold text-neutrals-white transition-all",
                  isOpen
                    ? "rounded-t-xl border-b border-secondary-red"
                    : "rounded-xl",
                  tab.id === "descripcion" && "bg-neutrals-verdiGris",
                  tab.id === "guia" && "bg-neutrals-moonStone",
                  tab.id === "nutricional" && "bg-neutrals-teal"
                )}
              >
                {tab.label}
              </button>
              <div
                className={clx(
                  "overflow-hidden rounded-b-xl bg-white transition-all duration-300 ease-in-out",
                  isOpen ? "max-h-[700px]" : "max-h-0"
                )}
              >
                <div className="px-1 py-1 text-center">
                  {tabContents[tab.id]}
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default TabInfo;
