const Ingredientes = ({ articulo }) => {
  return (
    <div className="md:py-3">
      <h4 className="font-gothamMedium text-lg">Ingredientes</h4>
      <p className="px-3 md:px-0 mt-2 font-corbert text-sm">
        {articulo.ingredientes}
      </p>
    </div>
  );
};

const ContNutricional = ({ articulo }) => {
  return (
    <>
      <Ingredientes articulo={articulo} />
      <h4 className="font-gothamMedium text-lg mt-2">Contenido nutricional</h4>

      <div className="mt-2 relative rounded-lg md:my-2 md:border md:border-secondary-red">
        <div className="absolute h-full border-l border-secondary-red left-1/2 translate-x-20 md:translate-x-52 lg:translate-x-60"></div>
        <div className="space-y-1 font-gotham text-xs md:text-base ">
          <div className="flex justify-between border-b border-secondary-red text-xs">
            <div className="px-3 py-1.5 w-full">Proteína bruta (Min)</div>
            <div className="pr-3 pl-0 py-1.5 w-1/4 text-left">
              {articulo?.proteina_bruta}%
            </div>
          </div>

          <div className="flex justify-between border-b border-secondary-red text-xs">
            <div className="px-3 py-1.5 w-full">Extracto etéreo (Min)</div>
            <div className="pr-3 pl-0 py-1.5 w-1/4 text-left">
              {articulo?.extracto_etereo}%
            </div>
          </div>

          <div className="flex justify-between border-b border-secondary-red text-xs">
            <div className="px-3 py-1.5 w-full">Fibra cruda (Máx)</div>
            <div className="pr-3 pl-0 py-1.5 w-1/4 text-left">
              {articulo?.fibra_cruda}%
            </div>
          </div>

          <div className="flex justify-between border-b border-secondary-red text-xs">
            <div className="px-3 py-1.5 w-full">Minerales totales (Máx)</div>
            <div className="pr-3 pl-0 py-1.5 w-1/4 text-left">
              {articulo?.minerales_totales}%
            </div>
          </div>

          <div className="flex justify-between border-b border-secondary-red text-xs">
            <div className="px-3 py-1.5 w-full">Humedad (Máx)</div>
            <div className="pr-3 pl-0 py-1.5 w-1/4 text-left">
              {articulo?.humedad}%
            </div>
          </div>

          <div className="flex justify-between border-b border-secondary-red text-xs">
            <div className="px-3 py-1.5 w-full">Calcio (Min)</div>
            <div className="pr-3 pl-0 py-1.5 w-1/4 text-left">
              {articulo?.calcio_min}%
            </div>
          </div>

          <div className="flex justify-between border-b border-secondary-red text-xs">
            <div className="px-3 py-1.5 w-full">Calcio (Máx)</div>
            <div className="pr-3 pl-0 py-1.5 w-1/4 text-left">
              {articulo?.calcio_max}%
            </div>
          </div>

          <div className="flex justify-between border-b border-secondary-red text-xs">
            <div className="px-3 py-1.5 w-full">Fósforo (Min)</div>
            <div className="pr-3 pl-0 py-1.5 w-1/4 text-left">
              {articulo?.fosforo_min}%
            </div>
          </div>

          <div className="flex justify-between border-b border-secondary-red text-xs">
            <div className="px-3 py-1.5 w-full">Fósforo (Máx)</div>
            <div className="pr-3 pl-0 py-1.5 w-1/4 text-left">
              {articulo?.fosforo_max}%
            </div>
          </div>

          <div className="flex justify-between border-b border-secondary-red text-xs">
            <div className="px-3 py-1.5 w-full">Energía metabolizable</div>
            <div className="pr-3 pl-0 py-1.5 w-1/4 text-left">
              {articulo?.energia_metabolizante} Kcal/kg
            </div>
          </div>

          {articulo?.taurina && (
            <div className="flex justify-between border-b border-secondary-red text-xs">
              <div className="px-3 py-1.5 w-full">Taurina</div>
              <div className="pr-3 pl-0 py-1.5 w-1/4 text-left">
                {articulo.taurina} mg/kg
              </div>
            </div>
          )}

          {articulo?.omega3 && (
            <div className="flex justify-between border-b border-secondary-red text-xs">
              <div className="px-3 py-1.5 w-full">Omega 3</div>
              <div className="pr-3 pl-0 py-1.5 w-1/4 text-left">
                {articulo.omega3}%
              </div>
            </div>
          )}

          {articulo?.omega6 && (
            <div className="flex justify-between border-b border-secondary-red text-xs">
              <div className="px-3 py-1.5 w-full">Omega 6</div>
              <div className="pr-3 pl-0 py-1.5 w-1/4 text-left">
                {articulo.omega6}%
              </div>
            </div>
          )}

          {articulo?.l_carnitina && (
            <div className="flex justify-between border-b border-secondary-red text-xs">
              <div className="px-3 py-1.5 w-full">L-Carnitina</div>
              <div className="pr-3 pl-0 py-1.5 w-1/4 text-left">
                {articulo.l_carnitina} mg/kg
              </div>
            </div>
          )}

          {articulo?.glucomanano && (
            <div className="flex justify-between border-b border-secondary-red text-xs">
              <div className="px-3 py-1.5 w-full">Glucomanano</div>
              <div className="pr-3 pl-0 py-1.5 w-1/4 text-left">
                {articulo.glucomanano} mg/kg
              </div>
            </div>
          )}

          {articulo?.condroitinsulfato && (
            <div className="flex justify-between border-b border-secondary-red text-xs">
              <div className="px-3 py-1.5 w-full">Condroitín sulfato</div>
              <div className="pr-3 pl-0 py-1.5 w-1/4 text-left">
                {articulo.condroitinsulfato} mg/kg
              </div>
            </div>
          )}

          {articulo?.glucosamina && (
            <div className="flex justify-between text-xs">
              <div className="px-3 py-1.5 w-full">Glucosamina</div>
              <div className="pr-3 pl-0 py-1.5 w-1/4 text-left">
                {articulo.glucosamina} mg/kg
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );
};
export default ContNutricional;
