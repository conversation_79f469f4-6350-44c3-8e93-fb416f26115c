const GuiaAlimentacion = () => {
  return (
    <>
    <h4 className="font-corbert text-lg">
      Racionamiento mínimo <PERSON> (gramos por día) (*)
    </h4>
    <div className="w-full max-w-4xl my-3 border border-tertiary-red  rounded-lg font-corbert text-sm">
      <table className="w-full border border-tertiary-red rounded-lg overflow-hidden bg-white">
        <thead>
          <tr>
            <th className="border-r border-tertiary-red bg-white p-4 text-center text-gray-700">
              Cantidad diaria
            </th>
            <th className="bg-white p-4 border-b border-tertiary-red text-center text-gray-700" colSpan={3}>
              Edad del gatito
            </th>
          </tr>
        </thead>
        <tbody>
          <tr className="">
            <td className="border-r border-tertiary-red p-4"></td>
            <td className="border-r border-tertiary-red bg-white p-4 text-center text-gray-700">
              Destete a 3 meses
            </td>
            <td className="border-r border-tertiary-red bg-white p-4 text-center text-gray-700">
              3 a 6 meses
            </td>
            <td className="bg-white p-4 text-center text-gray-700">6 a 12 meses</td>
          </tr>
          <tr className="border-t border-tertiary-red">
            <td className="border-r border-tertiary-red bg-white p-2 text-center text-gray-700">Min/Máx</td>
            <td className="border-r border-tertiary-red bg-white p-2 text-center text-gray-700">45 - 65 g</td>
            <td className="border-r border-tertiary-red bg-white p-2 text-center text-gray-700">65 - 80 g</td>
            <td className="bg-white p-2 text-center text-gray-700">80 - 70 g (**)</td>
          </tr>
        </tbody>
      </table>
    </div> 
    <p className="font-corbert text-lg pb-3">
      (*) Una taza equivale a 100g de alimento balanceado  
    </p> 
    <p className="font-corbert text-lg pb-3">
      (**) A medida que el gatito va creciendo, sus requerimientos calóricos disminuyen. Se recomienda disminuir de alimento para evitar exceso de peso.
    </p>
    </>
  )
}

export default GuiaAlimentacion; 