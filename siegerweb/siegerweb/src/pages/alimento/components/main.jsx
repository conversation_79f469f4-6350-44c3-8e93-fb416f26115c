import React from "react";

const ProductPageHero = ({ articulo, presentaciones }) => {
  return (
    <div className="pt-2 lg:pt-10 xl:pt-12 flex flex-col lg:flex-row items-center gap-3 lg:gap-5 justify-center w-full px-3 lg:px-10">
      <div className="max-w-md">
        <img src={articulo.url_frente} alt={articulo.name} />
      </div>
      <div className="flex flex-col lg:w-3/4 items-start">
        <h2 className="text-tertiary-red text-3xl lg:text-4xl xl:text-5xl 2xl:text-7xl font-gothamMedium xl:pr-20">
          {articulo.name}
        </h2>
        <p className="text-sm font-corbert text-[#A8ACB1] mt-2 pr-5 xl:pr-20">
          {articulo.descripcion}
        </p>
        <h4 className="font-gothamMedium tracking-tight text-lg mt-2 text-tertiary-gray ">
          PRESENTACIONES
        </h4>
        <p className="pb-3">
          {presentaciones ? (
            presentaciones.map((p, i) => (
              <span
                key={i}
                className="px-2.5 py-1.5 mr-1 rounded-md font-gothamMedium text-sm bg-neutrals-whiteSmoke text-neutrals-slateGray"
              >
                {p}
              </span>
            ))
          ) : (
            <span className="text-sm text-neutrals-slateGray font-gothamMedium">
              No hay presentaciones disponibles.
            </span>
          )}
        </p>
      </div>
    </div>
  );
};

export default ProductPageHero;
