import { useParams } from "react-router-dom";
import { useArticleById } from "@/hooks/queries/use-data";
import { useEffect } from "react";
import { LoaderCircle } from "lucide-react";
import ProductPageHero from "./components/main";
import CalculaTuPorcion from "../../components/calculate-portion";
import TabInfo from "./components/tab-info";
import { useSuscribePP } from "@/stores/use-suscribe-pp";

const ProductDetail = () => {
  const { id, slug: paramSlug } = useParams();
  const { data, isLoading, error } = useArticleById(id);
  const articulo = data?.articulo || {};
  const { setPP } = useSuscribePP();

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  useEffect(() => {
    setPP(true);
    return () => setPP(false);
  }, [setPP]);

  const presentaciones = articulo.presentacion
    ? articulo.presentacion.split(",").map((p) => p.trim())
    : [];

  return (
    <>
      {isLoading ? (
        <div className="flex h-screen w-full items-center justify-center">
          <LoaderCircle size={40} color="#8e8e8e" className="animate-spin" />
        </div>
      ) : error || !articulo ? (
        <p>Error al cargar el producto. Intente nuevamente.</p>
      ) : (
        <>
          <ProductPageHero
            articulo={articulo}
            presentaciones={presentaciones}
          />
          <TabInfo articulo={articulo} />
          <CalculaTuPorcion isPP={true} />
        </>
      )}
    </>
  );
};

export default ProductDetail;
