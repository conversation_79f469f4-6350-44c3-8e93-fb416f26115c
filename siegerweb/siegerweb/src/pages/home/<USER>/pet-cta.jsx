import React from "react";
import MedallaColorPerro1x from "@/assets/images/home/<USER>";
import MedallaColorPerro2x from "@/assets/images/home/<USER>";
import MedallaColorPerro3x from "@/assets/images/home/<USER>";
import MedallaColorGato1x from "@/assets/images/home/<USER>";
import MedallaColorGato2x from "@/assets/images/home/<USER>";
import MedallaColorGato3x from "@/assets/images/home/<USER>";
import MedallaPerro1x from "@/assets/images/home/<USER>";
import MedallaPerro2x from "@/assets/images/home/<USER>";
import MedallaPerro3x from "@/assets/images/home/<USER>";
import MedallaGato1x from "@/assets/images/home/<USER>";
import MedallaGato2x from "@/assets/images/home/<USER>";
import MedallaGato3x from "@/assets/images/home/<USER>";
import { Link } from "react-router-dom";
import { ArrowRight } from "lucide-react";

const PetsSection = () => {
  return (
    <div className="py-6 mb-5 w-full text-black px-5 bg-[linear-gradient(180deg,_rgba(242,242,242,0)_50%,_rgba(202,202,202,0.38)_100%)]">
      <h2 className="text-4xl text-center font-gothamBold">
        Eligí que mascota tenés <br className="hidden lg:block" />
        descubre su nutrición <span className="text-primary-red">ideal.</span>
      </h2>
      <p className="text-black font-corbert text-base text-center max-w-3xl m-auto block mt-3">
        Cada mascota tiene necesidades únicas que varían según su especie, edad
        y estilo de vida. Selecciona a tu compañero ideal, explora nuestros
        productos especialmente diseñados para ellos, y asegura su bienestar en
        cada etapa de su vida.
      </p>
      <div className="mt-14 flex flex-col md:flex-row gap-3 lg:gap-16 xl:gap-28 w-full items-center justify-center font-gotham pb-5 md:pb-2">
        {/* PERRO */}

        <div className="group cursor-pointer">
          <div className="relative w-[180px]">
            {/* Normal */}
            <picture>
              <source
                media="(min-width: 300px)"
                srcSet={`${MedallaPerro1x} 1x, ${MedallaPerro2x} 2x, ${MedallaPerro3x} 3x`}
              />
              <img
                src={MedallaPerro1x}
                alt="Medalla perro"
                className="w-[180px] absolute top-0 left-0 transition-opacity duration-300 group-hover:opacity-0"
              />
            </picture>

            {/* Color */}
            <picture>
              <source
                media="(min-width: 300px)"
                srcSet={`${MedallaColorPerro1x} 1x, ${MedallaColorPerro2x} 2x, ${MedallaColorPerro3x} 3x`}
              />
              <img
                src={MedallaColorPerro1x}
                alt="Medalla perro color"
                className="w-[180px] transition-opacity duration-300 opacity-0 group-hover:opacity-100"
              />
            </picture>
          </div>

          <Link to="">
            <button className="group mt-4 flex items-center justify-center gap-2 rounded-full bg-white w-full px-0 py-1 pr-0 text-tertiary-red transition-all duration-150 font-gothamMedium ">
              PERRO
              <div className="rounded-full bg-primary-red p-1 transition-all duration-150 group-hover:-rotate-45">
                <ArrowRight color="#fff" />
              </div>
            </button>
          </Link>
        </div>

        {/* GATO */}
        <div className="group cursor-pointer">
          <div className="relative w-[180px]">
            {/* Normal */}
            <picture>
              <source
                media="(min-width: 300px)"
                srcSet={`${MedallaGato1x} 1x, ${MedallaGato2x} 2x, ${MedallaGato3x} 3x`}
              />
              <img
                src={MedallaGato1x}
                alt="Medalla gato"
                className="w-[180px] absolute top-0 left-0 transition-opacity duration-300 group-hover:opacity-0"
              />
            </picture>

            {/* Color */}
            <picture>
              <source
                media="(min-width: 300px)"
                srcSet={`${MedallaColorGato1x} 1x, ${MedallaColorGato2x} 2x, ${MedallaColorGato3x} 3x`}
              />
              <img
                src={MedallaColorGato1x}
                alt="Medalla gato color"
                className="w-[180px] transition-opacity duration-300 opacity-0 group-hover:opacity-100"
              />
            </picture>
          </div>

          <Link to="">
            <button className="group mt-4 flex items-center justify-center gap-2 rounded-full bg-white w-full px-0 py-1 pr-0 text-tertiary-red transition-all duration-150 font-gothamMedium ">
              GATO
              <div className="rounded-full bg-primary-red p-1 transition-all duration-150 group-hover:-rotate-45">
                <ArrowRight color="#fff" />
              </div>
            </button>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default PetsSection;
