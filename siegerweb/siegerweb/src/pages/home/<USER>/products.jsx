import React, { useState } from "react";
import { Link } from "react-router-dom";
import useDevice from "@/hooks/use-device";
import PuppyBag1x from "@/assets/images/home/<USER>/puppy-bag.webp";
import PuppyBag2x from "@/assets/images/home/<USER>/<EMAIL>";
import PuppyBag3x from "@/assets/images/home/<USER>/<EMAIL>";
import KittenBag1x from "@/assets/images/home/<USER>/kitten-bag.webp";
import KittenBag2x from "@/assets/images/home/<USER>/<EMAIL>";
import KittenBag3x from "@/assets/images/home/<USER>/<EMAIL>";
import AdultDogBag1x from "@/assets/images/home/<USER>/adult-dog-bag.webp";
import AdultDogBag2x from "@/assets/images/home/<USER>/<EMAIL>";
import AdultDogBag3x from "@/assets/images/home/<USER>/<EMAIL>";
import SeniorDogBag1x from "@/assets/images/home/<USER>/senior-dog-bag.webp";
import SeniorDogBag2x from "@/assets/images/home/<USER>/<EMAIL>";
import SeniorDogBag3x from "@/assets/images/home/<USER>/<EMAIL>";
import DogCriadores1x from "@/assets/images/home/<USER>/dog-criadores.webp";
import DogCriadores2x from "@/assets/images/home/<USER>/<EMAIL>";
import DogCriadores3x from "@/assets/images/home/<USER>/<EMAIL>";
import DogPerformance1x from "@/assets/images/home/<USER>/dog-performance.webp";
import DogPerformance2x from "@/assets/images/home/<USER>/<EMAIL>";
import DogPerformance3x from "@/assets/images/home/<USER>/<EMAIL>";
import DogReduced1x from "@/assets/images/home/<USER>/dog-reduced.webp";
import DogReduced2x from "@/assets/images/home/<USER>/<EMAIL>";
import DogReduced3x from "@/assets/images/home/<USER>/<EMAIL>";
import KatzeAdult1x from "@/assets/images/home/<USER>/katze-adult.webp";
import KatzeAdult2x from "@/assets/images/home/<USER>/<EMAIL>";
import KatzeAdult3x from "@/assets/images/home/<USER>/<EMAIL>";
import KatzeCriadores1x from "@/assets/images/home/<USER>/katze-criadores.webp";
import KatzeCriadores2x from "@/assets/images/home/<USER>/<EMAIL>";
import KatzeCriadores3x from "@/assets/images/home/<USER>/<EMAIL>";
import KatzeDerma1x from "@/assets/images/home/<USER>/katze-derma.webp";
import KatzeDerma2x from "@/assets/images/home/<USER>/<EMAIL>";
import KatzeDerma3x from "@/assets/images/home/<USER>/<EMAIL>";
import KatzeHairball1x from "@/assets/images/home/<USER>/katze-hairball.webp";
import KatzeHairball2x from "@/assets/images/home/<USER>/<EMAIL>";
import KatzeHairball3x from "@/assets/images/home/<USER>/<EMAIL>";
import KatzeSenior1x from "@/assets/images/home/<USER>/katze-senior.webp";
import KatzeSenior2x from "@/assets/images/home/<USER>/<EMAIL>";
import KatzeSenior3x from "@/assets/images/home/<USER>/<EMAIL>";
import KatzeSterilized1x from "@/assets/images/home/<USER>/katze-sterilized.webp";
import KatzeSterilized2x from "@/assets/images/home/<USER>/<EMAIL>";
import KatzeSterilized3x from "@/assets/images/home/<USER>/<EMAIL>";
import KatzeUrinary1x from "@/assets/images/home/<USER>/katze-urinary.webp";
import KatzeUrinary2x from "@/assets/images/home/<USER>/<EMAIL>";
import KatzeUrinary3x from "@/assets/images/home/<USER>/<EMAIL>";

const Products = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [direction, setDirection] = useState("forward");
  const { isMobile, isTablet } = useDevice();

  const products = [
    // {
    //   nombre: "Puppy",
    //   id: "14",
    //   imagenes: {
    //     "1x": PuppyBag1x,
    //     "2x": PuppyBag2x,
    //     "3x": PuppyBag3x,
    //   },
    // },
    // {
    //   nombre: "Kitten",
    //   id: "",
    //   imagenes: {
    //     "1x": KittenBag1x,
    //     "2x": KittenBag2x,
    //     "3x": KittenBag3x,
    //   },
    // },
    // {
    //   nombre: "Adult",
    //   id: "",
    //   imagenes: {
    //     "1x": AdultDogBag1x,
    //     "2x": AdultDogBag2x,
    //     "3x": AdultDogBag3x,
    //   },
    // },
    // {
    //   nombre: "Senior",
    //   id: "",
    //   imagenes: {
    //     "1x": SeniorDogBag1x,
    //     "2x": SeniorDogBag2x,
    //     "3x": SeniorDogBag3x,
    //   },
    // },
    {
      nombre: "KATZE CRIADORES",
      id: "",
      imagenes: {
        "1x": KatzeCriadores1x,
        "2x": KatzeCriadores2x,
        "3x": KatzeCriadores3x,
      },
    },
    {
      nombre: "CRIADORES",
      id: "",
      imagenes: {
        "1x": DogCriadores1x,
        "2x": DogCriadores2x,
        "3x": DogCriadores3x,
      },
    },
    {
      nombre: "DERMAPROTECT",
      id: "",
      imagenes: {
        "1x": KatzeDerma1x,
        "2x": KatzeDerma2x,
        "3x": KatzeDerma3x,
      },
    },
    {
      nombre: "HIGH PERFORMANCE",
      id: "",
      imagenes: {
        "1x": DogPerformance1x,
        "2x": DogPerformance2x,
        "3x": DogPerformance3x,
      },
    },
    {
      nombre: "REDUCED CALORIE",
      id: "",
      imagenes: {
        "1x": DogReduced1x,
        "2x": DogReduced2x,
        "3x": DogReduced3x,
      },
    },
    {
      nombre: "KATZE ADULT",
      id: "",
      imagenes: {
        "1x": KatzeAdult1x,
        "2x": KatzeAdult2x,
        "3x": KatzeAdult3x,
      },
    },
    {
      nombre: "KATZE SENIOR",
      id: "",
      imagenes: {
        "1x": KatzeSenior1x,
        "2x": KatzeSenior2x,
        "3x": KatzeSenior3x,
      },
    },
    {
      nombre: "KATZE DERMAPROTECT",
      id: "",
      imagenes: {
        "1x": KatzeDerma1x,
        "2x": KatzeDerma2x,
        "3x": KatzeDerma3x,
      },
    },
    {
      nombre: "KATZE URINARY",
      id: "",
      imagenes: {
        "1x": KatzeUrinary1x,
        "2x": KatzeUrinary2x,
        "3x": KatzeUrinary3x,
      },
    },
    {
      nombre: "KATZE HAIRBALL",
      id: "",
      imagenes: {
        "1x": KatzeHairball1x,
        "2x": KatzeHairball2x,
        "3x": KatzeHairball3x,
      },
    },
    {
      nombre: "KATZE STERILIZED",
      id: "",
      imagenes: {
        "1x": KatzeSterilized1x,
        "2x": KatzeSterilized2x,
        "3x": KatzeSterilized3x,
      },
    },
  ];

  const itemsPerSlide = isMobile ? 3 : 4;
  const totalSlides = Math.ceil(products.length / itemsPerSlide);

  const goToSlide = (slideIndex) => {
    if (isTransitioning) return;
    setDirection(slideIndex > currentSlide ? "forward" : "backward");
    setIsTransitioning(true);
    setCurrentSlide(slideIndex);
    setTimeout(() => setIsTransitioning(false), 300);
  };

  const goToPrev = () => {
    if (isTransitioning || currentSlide === 0) return;
    setDirection("backward");
    setIsTransitioning(true);
    setCurrentSlide((prev) => prev - 1);
    setTimeout(() => setIsTransitioning(false), 300);
  };

  const goToNext = () => {
    if (isTransitioning || currentSlide === totalSlides - 1) return;
    setDirection("forward");
    setIsTransitioning(true);
    setCurrentSlide((prev) => prev + 1);
    setTimeout(() => setIsTransitioning(false), 300);
  };

  const getCurrentProducts = () => {
    const start = currentSlide * itemsPerSlide;
    return products.slice(start, start + itemsPerSlide);
  };

  const getAnimationClass = () => {
    if (!isTransitioning) return "";
    return direction === "forward"
      ? "animate-slide-in-right"
      : "animate-slide-out-left";
  };

  return (
    <div className="relative overflow-hidden">
      {/* Products Container */}
      <div
        className={`flex gap-2 mt-4 mx-3 justify-center transition-transform duration-300 ${getAnimationClass()} xl:min-h-[270px]`}
      >
        {getCurrentProducts().map((product, i) => (
          <Link
            key={`${currentSlide}-${i}`}
            to={`/${product.id}`}
            className="flex-shrink-0"
          >
            <div className="group flex flex-col items-center justify-center relative h-full w-[117px] md:w-32 lg:w-44 text-center border border-gray-300 rounded-md cursor-pointer">
              <picture>
                <source
                  srcSet={`${product.imagenes["1x"]} 1x, ${product.imagenes["2x"]} 2x, ${product.imagenes["3x"]} 3x`}
                />
                <img
                  src={product.imagenes["1x"]}
                  alt={product.nombre}
                  className="group-hover:scale-105 transition-transform duration-200"
                />
              </picture>
              <p className="absolute bottom-2 left-1/2 -translate-x-1/2 mt-2 text-xs lg:text-md font-gothamMedium">
                {product.nombre}
              </p>
            </div>
          </Link>
        ))}
      </div>

      {/* Navigation Arrows */}
      <button
        onClick={goToPrev}
        disabled={currentSlide === 0}
        className="absolute left-2 lg:left-20 top-1/2 -translate-y-1/2 z-10 p-2 rounded-full bg-white/80 hover:bg-white shadow-md disabled:opacity-50 disabled:cursor-not-allowed transition-opacity"
      >
        <svg
          className="w-5 h-5"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M15 19l-7-7 7-7"
          />
        </svg>
      </button>

      <button
        onClick={goToNext}
        disabled={currentSlide === totalSlides - 1}
        className="absolute right-2 lg:right-20 top-1/2 -translate-y-1/2 z-10 p-2 rounded-full bg-white/80 hover:bg-white shadow-md disabled:opacity-50 disabled:cursor-not-allowed transition-opacity"
      >
        <svg
          className="w-5 h-5"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M9 5l7 7-7 7"
          />
        </svg>
      </button>

      {/* Bullets/Indicators */}
      <div className="flex justify-center mt-4 space-x-2">
        {Array.from({ length: totalSlides }).map((_, idx) => (
          <button
            key={idx}
            onClick={() => goToSlide(idx)}
            disabled={isTransitioning}
            className={`w-3 h-3 rounded-full transition-colors duration-200 disabled:cursor-not-allowed ${
              currentSlide === idx ? "bg-secondary-red" : "bg-gray-300"
            }`}
          />
        ))}
      </div>
    </div>
  );
};

const ProductsSlider = () => {
  return (
    <div className="mt-10 pb-2">
      <h3 className="text-2xl text-center lg:text-left px-14 lg:px-0 lg:ml-5 md:text-3xl lg:text-4xl font-gothamMedium">
        Descubrí nuestros productos{" "}
        <span className="text-secondary-red">Sieger</span>
      </h3>
      <Products />
      <Link to="/productos" className="mt-4 block m-auto text-center w-fit">
        <button
          title="Ver todos"
          className="uppercase font-gothamMedium text-sm tracking-tight text-white px-3.5 py-2 rounded-full bg-tertiary-red shadow-md shadow-gray-400 hover:brightness-110 cursor-pointer transition-all duration-200"
        >
          Ver todos
        </button>
      </Link>
    </div>
  );
};

export default ProductsSlider;
