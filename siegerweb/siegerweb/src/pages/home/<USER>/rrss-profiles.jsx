import React from "react";
import facebook<PERSON>ogo from "@/assets/icons/facebook.svg";
import instagram<PERSON>ogo from "@/assets/icons/instagram.svg";
import youtubeLogo from "@/assets/icons/youtube.svg";

const RRSSProfiles = () => {
  return (
    <div className="mt-10">
      <div className="flex flex-col lg:flex-row items-center justify-between gap-2 lg:mr-14">
        <h3 className="text-2xl md:text-3xl lg:text-4xl font-gothamMedium ml-5">
          Nuestras redes <span className="text-secondary-red">sociales</span>
        </h3>
        <div className="flex flex-row gap-4 items-center justify-center">
          <a
            href="https://www.facebook.com/SiegerOficial"
            target="_blank"
            rel="noopener noreferrer"
          >
            <img
              src={facebookLogo}
              alt="Facebook"
              className="object-contain max-w-[9px]"
            />
          </a>
          <a
            href="https://www.instagram.com/siegeroficial/"
            target="_blank"
            rel="noopener noreferrer"
          >
            <img
              src={instagramLogo}
              alt="Instagram"
              className="aspect-square object-contain"
            />
          </a>
          <a
            href="https://www.youtube.com/@SiegerAr"
            target="_blank"
            rel="noopener noreferrer"
          >
            <img src={youtubeLogo} alt="Youtube" className="object-contain" />
          </a>
        </div>
      </div>
      <div className="flex flex-col lg:flex-row items-start justify-center gap-3 lg:gap-1 mt-4 px-1 lg:px-5 mx-2 lg:mx-12 lg:border lg:border-gray-300 lg:rounded-lg lg:shadow-lg lg:pb-2">
        <iframe
          src="https://www.instagram.com/siegeroficial/embed/"
          className="border border-gray-300 shadow-lg rounded-lg p-1 w-full lg:w-1/2 h-[400px] md:h-[500px] lg:h-[478px] xl:h-[458px] lg:border-none lg:shadow-none"
        ></iframe>

        <div className="w-full lg:w-1/2 h-[389px] min-[420px]:h-[460px] lg:h-[478px] xl:h-[458px] overflow-hidden border border-gray-300 rounded-lg shadow-lg lg:border-none lg:shadow-none">
          <iframe
            src="https://www.tiktok.com/embed/@sieger.oficial"
            allow="encrypted-media;"
            className="border border-gray-300 rounded-lg shadow-lg w-full h-full scale-[1.02] lg:scale-[1.068] lg:border-none lg:shadow-none"
            // style={{
            //   border: "none",
            //   transform: "scale(1.068)",
            //   transformOrigin: "center",
            // }}
          ></iframe>
        </div>
      </div>
    </div>
  );
};

export default RRSSProfiles;
