import React, { useState } from "react";
import { useBanners } from "@/hooks/queries/use-data";
import useDevice from "@/hooks/use-device";

const CarouselSlider = () => {
  const { isMobile, isTablet } = useDevice();

  const { data, isLoading, error } = useBanners();
  const banners = data?.banners || [];

  const [current, setCurrent] = useState(0);
  const total = banners.length;

  const goToSlide = (idx) => setCurrent(idx);
  const goToPrev = () =>
    setCurrent((prev) => (prev === 0 ? total - 1 : prev - 1));
  const goToNext = () =>
    setCurrent((prev) => (prev === total - 1 ? 0 : prev + 1));

  if (isLoading) {
    return (
      <div className="w-full h-96 flex items-center justify-center">
        Cargando...
      </div>
    );
  }

  if (error || banners.length === 0) {
    return null;
  }

  return (
    <div className="w-full">
      <div className="relative mb-4">
        <div className="overflow-hidden relative rounded-lg md:h-96">
          {banners.map((banner, idx) => (
            <div
              key={banner.id}
              className={`relative flex flex-col md:flex-row items-center justify-between transition-opacity duration-700 ease-in-out pl-16 h-full bg-red-50 ${
                current === idx ? "opacity-100 z-10" : "opacity-0 z-0"
              }`}
            >
              <div className="text-left ">
                <h2 className="text-4xl lg:text-5xl font-bold leading-snug z-40">
                  {banner.titulo}
                </h2>
                <p className="mt-2 text-gray-700">{banner.subtitulo}</p>
                <div className="mt-4 flex gap-2">
                  <a
                    href={banner.cta_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="bg-tertiary-red hover:brightness-105 transition-colors duration-200 text-white lg:px-4 px-2 py-2 rounded"
                  >
                    {banner.cta_text}
                  </a>
                </div>
              </div>
              <div className="absolute inset-0 w-full px-16 h-full -z-10">
                {banner.tipo === "video" ? (
                  <video
                    src={isMobile ? banner.media_mobile : banner.media_desktop}
                    autoPlay
                    muted
                    loop
                    className="w-full object-contain"
                  />
                ) : (
                  <img
                    src={isMobile ? banner.media_mobile : banner.media_desktop}
                    alt={banner.titulo}
                    className="w-full object-contain"
                  />
                )}
              </div>
            </div>
          ))}
        </div>
        {/* Indicators */}
        <div className="flex absolute bottom-5 left-1/2 z-30 space-x-3 -translate-x-1/2">
          {banners.map((_, idx) => (
            <button
              key={idx}
              type="button"
              className={`w-3 h-3 rounded-full ${
                current === idx ? "bg-tertiary-red" : "bg-gray-400"
              }`}
              aria-current={current === idx}
              aria-label={`Slide ${idx + 1}`}
              onClick={() => goToSlide(idx)}
            ></button>
          ))}
        </div>
        {/* Controls */}
        <button
          type="button"
          title="Anterior"
          className="flex absolute top-0 left-0 z-30 justify-center items-center px-3 h-full cursor-pointer group focus:outline-none"
          onClick={goToPrev}
        >
          <span className="inline-flex justify-center items-center w-8 h-8 rounded-full bg-white/30 dark:bg-gray-800/30 group-hover:bg-white/50 dark:group-hover:bg-gray-800/60 group-focus:ring-2 group-focus:ring-white dark:group-focus:ring-gray-800/70 group-focus:outline-none">
            <svg
              className="w-5 h-5 text-black dark:text-gray-800 -ml-0.5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M15 19l-7-7 7-7"
              ></path>
            </svg>
          </span>
        </button>
        <button
          type="button"
          title="Siguiente"
          className="flex absolute top-0 right-0 z-30 justify-center items-center px-3 h-full cursor-pointer group focus:outline-none"
          onClick={goToNext}
        >
          <span className="inline-flex justify-center items-center w-8 h-8 rounded-full bg-white/30 dark:bg-gray-800/30 group-hover:bg-white/50 dark:group-hover:bg-gray-800/60 group-focus:ring-2 group-focus:ring-white dark:group-focus:ring-gray-800/70 group-focus:outline-none">
            <svg
              className="w-5 h-5 text-white dark:text-gray-800"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M9 5l7 7-7 7"
              ></path>
            </svg>
          </span>
        </button>
      </div>
    </div>
  );
};

export default CarouselSlider;
