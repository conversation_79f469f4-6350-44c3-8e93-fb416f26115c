import LandingContent from "../../components/landing-sections/landing-content";
import useDevice from "@/hooks/use-device";
import headerMb1x from "@/assets/images/landings/perro/adult/header-mb.webp";
import headerMb2x from "@/assets/images/landings/perro/adult/<EMAIL>";
import headerMb3x from "@/assets/images/landings/perro/adult/<EMAIL>";
import header1x from "@/assets/images/landings/perro/adult/header.webp";
import header2x from "@/assets/images/landings/perro/adult/<EMAIL>";
import header3x from "@/assets/images/landings/perro/adult/<EMAIL>";
import beneficiosTitleMb1x from "@/assets/images/landings/perro/adult/beneficios-title-mb.webp";
import beneficiosTitleMb2x from "@/assets/images/landings/perro/adult/<EMAIL>";
import beneficiosTitleMb3x from "@/assets/images/landings/perro/adult/<EMAIL>";
import beneficiosTitle1x from "@/assets/images/landings/perro/adult/beneficios-title.webp";
import beneficiosTitle2x from "@/assets/images/landings/perro/adult/<EMAIL>";
import beneficiosTitle3x from "@/assets/images/landings/perro/adult/<EMAIL>";
import beneficiosMb1x from "@/assets/images/landings/perro/adult/beneficios-mb.webp";
import beneficiosMb2x from "@/assets/images/landings/perro/adult/<EMAIL>";
import beneficiosMb3x from "@/assets/images/landings/perro/adult/<EMAIL>";
import beneficios1x from "@/assets/images/landings/perro/adult/beneficios.webp";
import beneficios2x from "@/assets/images/landings/perro/adult/<EMAIL>";
import beneficios3x from "@/assets/images/landings/perro/adult/<EMAIL>";
import nutricionMb1x from "@/assets/images/landings/perro/adult/nutricion-mb.webp";
import nutricionMb2x from "@/assets/images/landings/perro/adult/<EMAIL>";
import nutricionMb3x from "@/assets/images/landings/perro/adult/<EMAIL>";
import nutricion1x from "@/assets/images/landings/perro/adult/nutricion.webp";
import nutricion2x from "@/assets/images/landings/perro/adult/<EMAIL>";
import nutricion3x from "@/assets/images/landings/perro/adult/<EMAIL>";

const DogAdult = () => {
  const { isMobile } = useDevice();

  return (
    <div>
      <LandingContent
        title="DogAdult"
        color="#0047BB"
        headerText="Nutrición completa y equilibrada para perros adultos, con ingredientes específicos para su edad y tamaño, que favorecen una digestión saludable, un pelaje brillante y una óptima vitalidad."
        heroImg={
          isMobile
            ? {
                "1x": headerMb1x,
                "2x": headerMb2x,
                "3x": headerMb3x,
              }
            : {
                "1x": header1x,
                "2x": header2x,
                "3x": header3x,
              }
        }
        beneficiosTitleImg={
          isMobile
            ? {
                "1x": beneficiosTitleMb1x,
                "2x": beneficiosTitleMb2x,
                "3x": beneficiosTitleMb3x,
              }
            : {
                "1x": beneficiosTitle1x,
                "2x": beneficiosTitle2x,
                "3x": beneficiosTitle3x,
              }
        }
        beneficiosImg={
          isMobile
            ? {
                "1x": beneficiosMb1x,
                "2x": beneficiosMb2x,
                "3x": beneficiosMb3x,
              }
            : {
                "1x": beneficios1x,
                "2x": beneficios2x,
                "3x": beneficios3x,
              }
        }
        nutritionImg={
          isMobile
            ? {
                "1x": nutricionMb1x,
                "2x": nutricionMb2x,
                "3x": nutricionMb3x,
              }
            : {
                "1x": nutricion1x,
                "2x": nutricion2x,
                "3x": nutricion3x,
              }
        }
      />
    </div>
  );
};

export default DogAdult;
