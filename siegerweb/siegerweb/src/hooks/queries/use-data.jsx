import { useQuery } from "@tanstack/react-query";
import {
  getArticleByID,
  getArticlesByMark,
  getArticles,
  getFilteredArticles,
} from "../../api/articles/get-data";
import { getBanners } from "../../api/banners/get-data";

export const useArticles = () => {
  return useQuery({
    queryKey: ["articulos"],
    queryFn: getArticles,
    keepPreviousData: true,
    refetchOnWindowFocus: false,
  });
};

export const useArticleById = (id) => {
  return useQuery({
    queryKey: ["articulo", id],
    queryFn: () => getArticleByID(id),
    // keepPreviousData: true,
    refetchOnWindowFocus: false,
    // enabled: !!id, // solo se ejecuta si hay un ID válido
  });
};

export const useArticlesByMark = (especie, mark) => {
  return useQuery({
    queryKey: ["articulos", "marca", especie, mark],
    queryFn: () => getArticlesByMark(especie, mark),
    // enabled: !!marca,
    refetchOnWindowFocus: false,
  });
};

export const useFilteredArticles = (filters = {}, pagination = {}) => {
  return useQuery({
    queryKey: ["articulos", "filtrados", filters, pagination],
    queryFn: () => {
      return getFilteredArticles({ ...filters, ...pagination });
    },
    keepPreviousData: false,
    refetchOnWindowFocus: false,
    staleTime: 1000,
    cacheTime: 300000,
    enabled: true,
  });
};

export const useBanners = () => {
  return useQuery({
    queryKey: ["banners"],
    queryFn: getBanners,
    keepPreviousData: true,
    refetchOnWindowFocus: false,
  });
};
