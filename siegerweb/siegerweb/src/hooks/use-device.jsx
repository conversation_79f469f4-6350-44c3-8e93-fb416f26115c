import useScreenSize from "@/hooks/use-screen-size";

const useDevice = () => {
  const { width } = useScreenSize();

  const isBigger = width >= 2200;
  const isFullHD = width >= 1520;
  const isDesktop = width >= 1024;
  const isTablet = width >= 768 && width < 1024;
  const isMobile = width < 768;

  return {
    isBigger,
    isFullHD,
    isDesktop,
    isTablet,
    isMobile,
  };
};

export default useDevice;
