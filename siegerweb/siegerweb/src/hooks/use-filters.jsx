import { useState, useCallback } from "react";

export function useFilters(initialFilters = {}) {
  const [filters, setFilters] = useState(initialFilters);

  const updateFilter = useCallback((categoryId, optionValue, checked) => {
    setFilters((prev) => {
      const categoryFilters = prev[categoryId] || [];

      if (checked) {
        // Add filter if not already present
        if (!categoryFilters.includes(optionValue)) {
          return {
            ...prev,
            [categoryId]: [...categoryFilters, optionValue],
          };
        }
      } else {
        // Remove filter
        return {
          ...prev,
          [categoryId]: categoryFilters.filter(
            (value) => value !== optionValue
          ),
        };
      }

      return prev;
    });
  }, []);

  const clearCategory = useCallback((categoryId) => {
    setFilters((prev) => ({
      ...prev,
      [categoryId]: [],
    }));
  }, []);

  const clearAllFilters = useCallback(() => {
    setFilters({});
  }, []);

  const getActiveFiltersCount = useCallback(() => {
    if (!filters) return;
    return Object.values(filters).reduce((total, categoryFilters) => {
      return total + categoryFilters.length;
    }, 0);
  }, [filters]);

  // Convert internal filter state to API query format
  const getFilterQuery = useCallback(() => {
    return {
      especie:
        filters?.especie?.length === 2
          ? ""
          : filters?.especie?.length
          ? filters.especie[0]
          : undefined,
      etapas: filters?.etapas?.length ? filters.etapas : undefined,
      tipo:
        filters?.tipo?.length === 2
          ? ""
          : filters?.tipo?.length
          ? filters.tipo[0]
          : undefined,
      beneficios: filters?.beneficios?.length ? filters.beneficios : undefined,
    };
  }, [filters]);

  return {
    filters,
    updateFilter,
    clearCategory,
    clearAllFilters,
    getActiveFiltersCount,
    getFilterQuery,
  };
}
